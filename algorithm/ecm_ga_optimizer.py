#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于遗传算法(GA)的ECM电池参数识别器
将原PSO算法替换为GA算法，保持其他功能完全一致

主要功能:
1. OCV-SOC曲线参数识别
2. 支持使用实际SOC数据确定初始SOC范围
3. 提供完整的可视化和验证功能
4. 使用DEAP库实现遗传算法优化

作者: 基于ecm_notebook_original.py修改
日期: 2025-01-04
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from collections import defaultdict
import random
import time
import warnings
warnings.filterwarnings('ignore')

# 遗传算法相关导入
from deap import base, creator, tools, algorithms

# CUDA并行计算相关导入
try:
    import cupy as cp
    CUDA_AVAILABLE = True
    print("✓ CUDA支持已启用 (CuPy)")
except ImportError:
    import numpy as cp  # 回退到numpy
    CUDA_AVAILABLE = False
    print("⚠ CUDA不可用，使用CPU计算")

# 多进程并行计算
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import os

# 设置中文字体显示
try:
    from font_config import setup_chinese_font
    setup_chinese_font()
except ImportError:
    # 备用字体设置
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False


class BatteryParameterOptimizerGA:
    """基于遗传算法的ECM电池参数优化器（支持CUDA加速）"""

    def __init__(self, data, current_col, voltage_col, use_cuda=True, n_workers=None):
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col

        # CUDA和并行设置
        self.use_cuda = use_cuda and CUDA_AVAILABLE
        self.n_workers = n_workers or min(mp.cpu_count(), 8)

        # 更密集的SOC点 (0%-100%，每2%一个点)
        self.soc_points = np.array([
            0.0, 1.0, 2.0, 3.0, 5.0, 7.0, 10.0, 15.0, 20.0,   # 放电截止处密集 (0-20%)
            30.0, 40.0, 50.0, 60.0, 70.0, 80.0,               # 平台期稀疏 (20-80%)
            85.0, 90.0, 93.0, 95.0, 97.0, 98.0, 99.0, 100.0   # 充电截止处密集 (80-100%)
        ])
        
        # 固定R0值 (所有SOC点使用相同内阻)
        self.fixed_r0 = 0.0006  # 固定内阻值(Ω)
        
        # 默认OCV曲线和允许波动范围 (±0.1V)
        # 初始使用线性插值生成默认曲线，后面会被优化
        self.default_ocv = np.array([
            2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 3.10, 3.20,  # 对应放电截止的电压
            3.28, 3.30, 3.31, 3.32, 3.33, 3.34,                    # 对应平台期的电压
            3.36, 3.38, 3.40, 3.41, 3.42, 3.43, 3.44, 3.45         # 对应充电截止的电压
        ])
        self.ocv_bounds = (self.default_ocv - 0.3, self.default_ocv + 0.3)
        
        # 确保SOC点和OCV点长度一致
        assert len(self.soc_points) == len(self.default_ocv), "SOC点和OCV点长度必须一致"
        
        # 优化过程记录
        self.optimization_history = {
            'generation': [],
            'best_fitness': [],
            'best_ocv': []
        }
        
        # GA算法相关变量
        self.lb = None  # 下边界
        self.ub = None  # 上边界
        self.toolbox = None

        # 初始化CUDA数据（在所有属性定义完成后）
        if self.use_cuda:
            print(f"✓ 启用CUDA加速计算")
            # 将数据转移到GPU
            self._setup_cuda_data()
        else:
            print(f"✓ 使用CPU并行计算 ({self.n_workers}个进程)")

        print(f"计算设备: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")

    def _setup_cuda_data(self):
        """设置CUDA数据"""
        if not self.use_cuda:
            return

        try:
            # 将关键数据转移到GPU
            self.data_gpu = {}
            self.data_gpu['current'] = cp.array(self.data[self.current_col].values)
            self.data_gpu['voltage'] = cp.array(self.data[self.voltage_col].values)
            self.data_gpu['time_diff'] = cp.array(self._calculate_time_diff())
            self.data_gpu['soc_points'] = cp.array(self.soc_points)
            self.data_gpu['default_ocv'] = cp.array(self.default_ocv)
            self.data_gpu['ocv_bounds_lower'] = cp.array(self.ocv_bounds[0])
            self.data_gpu['ocv_bounds_upper'] = cp.array(self.ocv_bounds[1])

            print(f"✓ 数据已转移到GPU内存")
        except Exception as e:
            print(f"⚠ GPU数据设置失败，回退到CPU: {e}")
            self.use_cuda = False

    def _calculate_time_diff(self):
        """计算时间差（小时）"""
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')
        return delta_t
    
    def simulate_battery(self, params, plot_comparison=False):
        """使用给定参数运行电池模拟（支持CUDA加速）"""
        if self.use_cuda:
            return self._simulate_battery_cuda(params, plot_comparison)
        else:
            return self._simulate_battery_cpu(params, plot_comparison)

    def _simulate_battery_cuda(self, params, plot_comparison=False):
        """CUDA加速的电池模拟"""
        initial_soc = params[0]
        ocv_points = cp.array(params[1:])  # 转换为GPU数组
        capacity_Ah = 280  # 固定容量

        try:
            # GPU上的线性插值（简化版本）
            soc_gpu = self.data_gpu['soc_points']

            # 计算SOC
            current_gpu = self.data_gpu['current']
            delta_t_gpu = self.data_gpu['time_diff']
            delta_soc = current_gpu * delta_t_gpu / capacity_Ah * 100
            soc = initial_soc - cp.cumsum(delta_soc)
            soc = cp.clip(soc, 0, 100)

            # GPU上的线性插值计算OCV
            ocv = self._cuda_linear_interp(soc, soc_gpu, ocv_points)

            # 计算端电压
            voltage = ocv - current_gpu * self.fixed_r0

            # 计算MAE
            mae = cp.mean(cp.abs(voltage - self.data_gpu['voltage']))

            # 转回CPU用于绘图
            if plot_comparison:
                voltage_cpu = cp.asnumpy(voltage)
                soc_cpu = cp.asnumpy(soc)
                ocv_cpu = cp.asnumpy(ocv)
                # 创建插值函数用于绘图
                ocv_interp = interp1d(self.soc_points, cp.asnumpy(ocv_points),
                                     kind='linear', fill_value="extrapolate")
                self._plot_comparison(voltage_cpu, soc_cpu, initial_soc, capacity_Ah, ocv_interp)

            return float(cp.asnumpy(mae))

        except Exception as e:
            print(f"CUDA模拟失败，回退到CPU: {e}")
            return self._simulate_battery_cpu(params, plot_comparison)

    def _simulate_battery_cpu(self, params, plot_comparison=False):
        """CPU版本的电池模拟"""
        initial_soc = params[0]
        ocv_points = params[1:]  # 所有剩余参数都是OCV点
        capacity_Ah = 280  # 固定容量

        # 验证参数长度
        assert len(ocv_points) == len(self.soc_points), "OCV点数量必须与SOC点相同"

        try:
            # 创建OCV插值函数
            ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear',
                                 fill_value="extrapolate")
        except ValueError as e:
            raise ValueError(f"插值函数创建失败: {str(e)}")

        # 计算时间差（小时）
        delta_t = self._calculate_time_diff()

        # 计算SOC
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / capacity_Ah * 100
        soc = initial_soc - np.cumsum(delta_soc)
        soc = np.clip(soc, 0, 100)

        # 计算端电压 (使用固定R0)
        ocv = ocv_interp(soc)
        voltage = ocv - current_values * self.fixed_r0

        # 计算MAE
        mae = np.mean(np.abs(voltage - self.data[self.voltage_col]))

        # 绘图
        if plot_comparison:
            self._plot_comparison(voltage, soc, initial_soc, capacity_Ah, ocv_interp)

        return mae

    def _cuda_linear_interp(self, x, xp, fp):
        """CUDA线性插值函数"""
        # 简化的线性插值实现
        indices = cp.searchsorted(xp, x, side='right') - 1
        indices = cp.clip(indices, 0, len(xp) - 2)

        x0 = xp[indices]
        x1 = xp[indices + 1]
        y0 = fp[indices]
        y1 = fp[indices + 1]

        # 线性插值公式
        t = (x - x0) / (x1 - x0)
        return y0 + t * (y1 - y0)
    
    def _plot_comparison(self, sim_voltage, sim_soc, initial_soc, capacity_Ah, ocv_interp):
        """绘制对比图"""
        plt.figure(figsize=(12, 10))
        
        # 电压对比
        plt.subplot(3, 1, 1)
        plt.plot(self.data.index, self.data[self.voltage_col], 'b-', label='真实电压')
        plt.plot(self.data.index, sim_voltage, 'r--', label='模拟电压')
        plt.ylabel('电压(V)')
        plt.title(f'电池模拟 (容量={capacity_Ah}Ah, 初始SOC={initial_soc}%, 固定R0={self.fixed_r0}Ω)')
        plt.legend()
        plt.grid(True)
        
        # SOC变化
        plt.subplot(3, 1, 2)
        plt.plot(self.data.index, sim_soc, 'g-', label='模拟SOC')
        if '2023-12-25_1#BMS-堆内-1簇组SOC' in self.data.columns:
            plt.plot(self.data.index, self.data['2023-12-25_1#BMS-堆内-1簇组SOC'], 'r-', label='BMS_SOC')
        plt.ylabel('SOC(%)')
        plt.legend()
        plt.grid(True)
        
        # OCV变化
        plt.subplot(3, 1, 3)
        plt.plot(self.data.index, ocv_interp(sim_soc), 'm-', label='OCV')
        plt.xlabel('时间')
        plt.ylabel('OCV(V)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def objective_function(self, params):
        """目标函数，只优化OCV曲线（与原版本完全相同）"""
        # 提取参数 (初始SOC + OCV点)
        ocv_points = params[1:]
        
        # 1. OCV约束检查
        ocv_penalty = 0
        
        # 检查OCV是否在允许范围内
        if not np.all((ocv_points >= self.ocv_bounds[0]) & (ocv_points <= self.ocv_bounds[1])):
            # 计算超出边界的惩罚
            lower_violation = np.sum(np.maximum(self.ocv_bounds[0] - ocv_points, 0))
            upper_violation = np.sum(np.maximum(ocv_points - self.ocv_bounds[1], 0))
            ocv_penalty += 100 * (lower_violation + upper_violation)
        
        # 检查OCV单调性
        ocv_diff = np.diff(ocv_points)
        if not np.all(ocv_diff >= 0):
            ocv_penalty += 0.05 * np.sum(np.abs(ocv_diff[ocv_diff < 0]))
        
        # 2. 运行模拟计算MAE
        try:
            mae = self.simulate_battery(params)
        except Exception as e:
            print(f"模拟失败: {str(e)}")
            return (1e6,)  # DEAP需要返回元组
        
        # 总目标值 = MAE + OCV惩罚项
        fitness = mae + ocv_penalty
        return (fitness,)  # DEAP需要返回元组
    
    def _setup_ga(self, population_size=100):
        """设置遗传算法"""
        # 创建适应度类和个体类
        creator.create("FitnessMin", base.Fitness, weights=(-1.0,))  # 最小化问题
        creator.create("Individual", list, fitness=creator.FitnessMin)
        
        # 创建工具箱
        self.toolbox = base.Toolbox()
        
        # 参数维度
        n_params = len(self.lb)
        
        # 注册基因生成函数
        for i in range(n_params):
            self.toolbox.register(f"attr_{i}", random.uniform, self.lb[i], self.ub[i])
        
        # 注册个体和种群生成函数
        self.toolbox.register("individual", tools.initCycle, creator.Individual,
                             [getattr(self.toolbox, f"attr_{i}") for i in range(n_params)], n=1)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 注册遗传算子
        self.toolbox.register("evaluate", self.objective_function)
        self.toolbox.register("mate", tools.cxBlend, alpha=0.3)  # 混合交叉
        self.toolbox.register("mutate", self._bounded_mutation, indpb=0.1)  # 有界变异
        self.toolbox.register("select", tools.selTournament, tournsize=3)  # 锦标赛选择
    
    def _bounded_mutation(self, individual, indpb):
        """有界变异操作"""
        for i in range(len(individual)):
            if random.random() < indpb:
                # 高斯变异
                sigma = (self.ub[i] - self.lb[i]) * 0.1  # 变异强度为范围的10%
                individual[i] += random.gauss(0, sigma)
                # 边界约束
                individual[i] = max(self.lb[i], min(self.ub[i], individual[i]))
        return (individual,)

    def _parallel_evaluate(self, population):
        """并行评估种群适应度"""
        if self.use_cuda:
            # CUDA模式：批量评估
            return self._cuda_batch_evaluate(population)
        else:
            # CPU模式：多进程并行评估
            return self._cpu_parallel_evaluate(population)

    def _cuda_batch_evaluate(self, population):
        """CUDA真正的批量并行评估"""
        try:
            return self._cuda_batch_evaluate_optimized(population)
        except Exception as e:
            print(f"CUDA批量评估失败，回退到逐个评估: {e}")
            # 回退到逐个评估但仍使用GPU
            fitnesses = []
            for individual in population:
                fitness = self.objective_function(individual)
                fitnesses.append(fitness)  # objective_function已经返回元组
            return fitnesses

    def _cuda_batch_evaluate_optimized(self, population):
        """优化的CUDA批量并行评估"""
        if not self.use_cuda or not population:
            return [self.objective_function(ind) for ind in population]  # 已经是元组格式

        # 转换种群为GPU数组
        pop_array = cp.array(population)  # shape: (pop_size, n_params)
        batch_size = len(population)

        # 分离初始SOC和OCV参数
        initial_socs = pop_array[:, 0]  # shape: (batch_size,)
        ocv_params = pop_array[:, 1:]   # shape: (batch_size, n_ocv_points)

        # 批量计算所有个体的适应度
        fitnesses = self._cuda_batch_simulate(initial_socs, ocv_params)

        # DEAP需要元组格式的适应度值
        return [(float(f),) for f in cp.asnumpy(fitnesses)]

    def _cuda_batch_simulate(self, initial_socs_batch, ocv_params_batch):
        """CUDA批量电池模拟"""
        batch_size = len(initial_socs_batch)
        capacity_Ah = 280

        # 获取预加载的GPU数据
        current_gpu = self.data_gpu['current']  # shape: (n_timesteps,)
        voltage_gpu = self.data_gpu['voltage']  # shape: (n_timesteps,)
        delta_t_gpu = self.data_gpu['time_diff']  # shape: (n_timesteps,)
        soc_points_gpu = self.data_gpu['soc_points']  # shape: (n_soc_points,)

        n_timesteps = len(current_gpu)

        # 扩展数据以支持批量计算
        current_batch = cp.tile(current_gpu, (batch_size, 1))  # shape: (batch_size, n_timesteps)
        voltage_batch = cp.tile(voltage_gpu, (batch_size, 1))  # shape: (batch_size, n_timesteps)
        delta_t_batch = cp.tile(delta_t_gpu, (batch_size, 1))  # shape: (batch_size, n_timesteps)

        # 批量计算SOC
        delta_soc_batch = current_batch * delta_t_batch / capacity_Ah * 100

        # 为每个个体计算SOC轨迹
        initial_socs_expanded = initial_socs_batch[:, cp.newaxis]  # shape: (batch_size, 1)
        soc_batch = initial_socs_expanded - cp.cumsum(delta_soc_batch, axis=1)
        soc_batch = cp.clip(soc_batch, 0, 100)  # shape: (batch_size, n_timesteps)

        # 批量线性插值计算OCV
        ocv_batch = self._cuda_batch_linear_interp(soc_batch, soc_points_gpu, ocv_params_batch)

        # 批量计算端电压
        voltage_sim_batch = ocv_batch - current_batch * self.fixed_r0

        # 批量计算MAE
        mae_batch = cp.mean(cp.abs(voltage_sim_batch - voltage_batch), axis=1)

        # 批量约束检查
        constraint_penalties = self._cuda_batch_constraint_check(ocv_params_batch)

        # 总适应度 = MAE + 约束惩罚
        fitness_batch = mae_batch + constraint_penalties

        return fitness_batch

    def _cuda_batch_linear_interp(self, x_batch, xp, fp_batch):
        """CUDA批量线性插值

        Args:
            x_batch: shape (batch_size, n_points) - 要插值的x值
            xp: shape (n_soc_points,) - 已知x点
            fp_batch: shape (batch_size, n_soc_points) - 每个个体对应的y值

        Returns:
            shape (batch_size, n_points) - 插值结果
        """
        batch_size, n_points = x_batch.shape
        n_soc_points = len(xp)

        # 为每个时间点找到插值区间
        # 使用广播进行批量搜索
        x_expanded = x_batch[:, :, cp.newaxis]  # shape: (batch_size, n_points, 1)
        xp_expanded = xp[cp.newaxis, cp.newaxis, :]  # shape: (1, 1, n_soc_points)

        # 找到右侧索引
        indices = cp.sum(x_expanded >= xp_expanded, axis=2) - 1  # shape: (batch_size, n_points)
        indices = cp.clip(indices, 0, n_soc_points - 2)

        # 获取插值点
        batch_indices = cp.arange(batch_size)[:, cp.newaxis]  # shape: (batch_size, 1)

        # 左右端点的x值
        x0 = xp[indices]  # shape: (batch_size, n_points)
        x1 = xp[indices + 1]  # shape: (batch_size, n_points)

        # 左右端点的y值
        y0 = fp_batch[batch_indices, indices]  # shape: (batch_size, n_points)
        y1 = fp_batch[batch_indices, indices + 1]  # shape: (batch_size, n_points)

        # 线性插值
        t = (x_batch - x0) / (x1 - x0)
        result = y0 + t * (y1 - y0)

        return result

    def _cuda_batch_constraint_check(self, ocv_params_batch):
        """CUDA批量约束检查

        Args:
            ocv_params_batch: shape (batch_size, n_ocv_points)

        Returns:
            shape (batch_size,) - 每个个体的约束惩罚
        """
        batch_size = ocv_params_batch.shape[0]

        # 获取边界
        lower_bounds = cp.array(self.ocv_bounds[0])  # shape: (n_ocv_points,)
        upper_bounds = cp.array(self.ocv_bounds[1])  # shape: (n_ocv_points,)

        # 边界违反检查
        lower_violations = cp.sum(cp.maximum(lower_bounds - ocv_params_batch, 0), axis=1)
        upper_violations = cp.sum(cp.maximum(ocv_params_batch - upper_bounds, 0), axis=1)
        boundary_penalty = 100 * (lower_violations + upper_violations)

        # 单调性检查
        ocv_diff = cp.diff(ocv_params_batch, axis=1)  # shape: (batch_size, n_ocv_points-1)
        monotonic_violations = cp.sum(cp.abs(cp.minimum(ocv_diff, 0)), axis=1)
        monotonic_penalty = 0.05 * monotonic_violations

        return boundary_penalty + monotonic_penalty

    def _cpu_parallel_evaluate(self, population):
        """CPU多进程并行评估"""
        try:
            # 使用线程池而不是进程池，避免序列化问题
            with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                fitnesses = list(executor.map(self.objective_function, population))
            return fitnesses
        except Exception as e:
            print(f"并行评估失败，回退到串行: {e}")
            # 回退到串行评估
            return [self.objective_function(ind) for ind in population]

    def _plot_optimization_process(self):
        """绘制优化过程曲线"""
        if not self.optimization_history['generation']:
            return

        plt.figure(figsize=(15, 5))

        # 1. 适应度变化曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.optimization_history['generation'],
                self.optimization_history['best_fitness'], 'b-')
        plt.xlabel('代数')
        plt.ylabel('目标函数值')
        plt.title('GA优化过程收敛曲线')
        plt.grid(True)

        # 2. OCV曲线进化过程
        plt.subplot(1, 2, 2)
        for i in range(0, len(self.optimization_history['generation']), 10):
            plt.plot(self.soc_points, self.optimization_history['best_ocv'][i],
                    alpha=0.1, color='blue')
        plt.plot(self.soc_points, self.optimization_history['best_ocv'][-1],
                'r-', linewidth=2, label='最终OCV曲线')
        plt.plot(self.soc_points, self.default_ocv, 'g--', label='默认OCV曲线')
        plt.fill_between(self.soc_points,
                        self.ocv_bounds[0],
                        self.ocv_bounds[1],
                        color='gray', alpha=0.2, label='允许波动范围')
        plt.xlabel('SOC(%)')
        plt.ylabel('OCV(V)')
        plt.title('OCV曲线优化过程')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        plt.show()

    def optimize_parameters(self, use_actual_soc=True, soc_tolerance=1,
                          population_size=100, n_generations=150,
                          crossover_prob=0.7, mutation_prob=0.2):
        """
        运行遗传算法参数优化

        参数:
            use_actual_soc: 是否使用实际SOC数据确定初始SOC范围
            soc_tolerance: SOC容差范围(%)，用于设定搜索边界
            population_size: 种群大小
            n_generations: 进化代数
            crossover_prob: 交叉概率
            mutation_prob: 变异概率
        """
        if use_actual_soc:
            # 方案：使用实际SOC数据确定初始SOC范围
            # 自动检测SOC列
            soc_cols = [col for col in self.data.columns if 'SOC' in col]

            if soc_cols:
                soc_col_name = soc_cols[0]  # 使用第一个找到的SOC列
                print(f"检测到SOC列: {soc_col_name}")

                # 获取实际初始SOC
                actual_initial_soc = self.data[soc_col_name].iloc[0]
                print(f"数据集实际初始SOC: {actual_initial_soc:.2f}%")

                # 设定搜索范围：实际值 ± 容差
                soc_lower = max(0, actual_initial_soc - soc_tolerance)
                soc_upper = min(100, actual_initial_soc + soc_tolerance)

                self.lb = [soc_lower] + list(self.ocv_bounds[0])
                self.ub = [soc_upper] + list(self.ocv_bounds[1])

                print(f"初始SOC搜索范围: {soc_lower:.1f}% - {soc_upper:.1f}% (±{soc_tolerance}%容差)")
            else:
                print("警告: 未找到SOC列，使用默认范围")
                self.lb = [4] + list(self.ocv_bounds[0])
                self.ub = [6] + list(self.ocv_bounds[1])
                print("使用默认初始SOC搜索范围: 4% - 6%")
        else:
            # 原始方案：使用固定范围
            self.lb = [4] + list(self.ocv_bounds[0])
            self.ub = [6] + list(self.ocv_bounds[1])
            print("使用默认初始SOC搜索范围: 4% - 6%")

        # 设置遗传算法
        self._setup_ga(population_size)

        # 运行GA优化
        print(f"开始遗传算法优化(固定R0={self.fixed_r0:.4f}Ω)...")
        print(f"GA参数: 种群={population_size}, 代数={n_generations}, 交叉率={crossover_prob}, 变异率={mutation_prob}")

        # 记录开始时间
        start_time = time.time()

        # 创建初始种群
        population = self.toolbox.population(n=population_size)

        # 并行评估初始种群
        print("评估初始种群...")
        fitnesses = self._parallel_evaluate(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit

        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)

        # 进化过程
        generation_start_time = time.time()

        for generation in range(n_generations):
            gen_start = time.time()

            # 选择下一代
            offspring = self.toolbox.select(population, len(population))
            offspring = list(map(self.toolbox.clone, offspring))

            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < crossover_prob:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values

            for mutant in offspring:
                if random.random() < mutation_prob:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values

            # 并行评估需要重新评估的个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            if invalid_ind:
                fitnesses = self._parallel_evaluate(invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit

            # 替换种群
            population[:] = offspring

            # 记录最佳个体
            best_ind = tools.selBest(population, 1)[0]
            best_fitness = best_ind.fitness.values[0]

            self.optimization_history['generation'].append(generation + 1)
            self.optimization_history['best_fitness'].append(best_fitness)
            self.optimization_history['best_ocv'].append(best_ind[1:].copy())

            # 打印进度（包含时间信息）
            gen_time = time.time() - gen_start
            elapsed_time = time.time() - generation_start_time

            if (generation + 1) % 10 == 0:
                record = stats.compile(population)
                avg_gen_time = elapsed_time / (generation + 1)
                eta = avg_gen_time * (n_generations - generation - 1)

                print(f"代数 {generation + 1}/{n_generations}: "
                      f"最佳={best_fitness:.6f}, 平均={record['avg']:.6f}, "
                      f"本代用时={gen_time:.2f}s, 已用时={elapsed_time:.1f}s, "
                      f"预计剩余={eta:.1f}s")
            elif (generation + 1) % 50 == 0:
                # 每50代显示更详细信息
                record = stats.compile(population)
                progress = (generation + 1) / n_generations * 100
                print(f"进度 {progress:.1f}%: 最佳适应度={best_fitness:.6f}, "
                      f"初始SOC={best_ind[0]:.2f}%, 已用时={elapsed_time:.1f}s")

        # 获取最优解
        best_individual = tools.selBest(population, 1)[0]
        best_params = list(best_individual)
        best_mae = best_individual.fitness.values[0]

        # 计算总运行时间
        total_time = time.time() - start_time

        print("\n" + "="*60)
        print("🎉 遗传算法优化完成!")
        print(f"最小MAE: {best_mae:.4f} V")
        print(f"总运行时间: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)")
        print(f"平均每代时间: {total_time/n_generations:.2f} 秒")
        print(f"计算设备: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")
        print(f"并行线程数: {self.n_workers}")
        print("="*60)

        # 解包最优参数
        initial_soc = best_params[0]
        ocv_points = best_params[1:]

        print(f"\n初始SOC: {initial_soc:.2f}%")
        print("\n优化后的OCV曲线点:")
        for i in range(0, len(ocv_points), 5):  # 每5个点打印一个
            soc = self.soc_points[i]
            point = ocv_points[i]
            default = self.default_ocv[i]
            print(f"SOC {soc:.0f}%: {point:.4f}V (默认:{default:.2f}V, 偏差:{point-default:+.3f}V)")

        # 绘制优化过程曲线
        print("\n📊 生成优化过程图...")
        plot_start = time.time()
        self._plot_optimization_process()
        plot_time = time.time() - plot_start

        # 用最优参数运行模拟并绘图
        print(f"📈 生成验证图... (优化过程图用时: {plot_time:.1f}s)")
        verify_start = time.time()
        self.simulate_battery(best_params, plot_comparison=True)
        verify_time = time.time() - verify_start

        print(f"\n⏱️  时间统计:")
        print(f"   优化算法: {total_time:.2f}s")
        print(f"   优化过程图: {plot_time:.1f}s")
        print(f"   验证图: {verify_time:.1f}s")
        print(f"   总计: {total_time + plot_time + verify_time:.2f}s")

        return best_params, best_mae


# 使用示例
if __name__ == "__main__":
    # 加载数据
    data = pd.read_hdf("./database/127_1_1_2023-12-25.h5")

    # 示例1: 使用遗传算法优化OCV参数（支持CUDA加速）
    print("=== 基于遗传算法的ECM参数识别（CUDA加速版）===")
    optimizer_ga = BatteryParameterOptimizerGA(
        data=data,
        current_col="2023-12-25_1#BMS-堆内-1簇组电流",
        voltage_col="2023-12-25_1#BMS-堆内-1簇单体电压1",
        use_cuda=True,      # 启用CUDA加速
        n_workers=8         # CPU并行进程数（CUDA不可用时使用）
    )

    # 方案1: 使用实际SOC数据 (推荐)
    print("--- 使用实际SOC数据确定初始范围 ---")

    # 记录开始时间
    method1_start = time.time()

    best_params_actual, best_mae_actual = optimizer_ga.optimize_parameters(
        use_actual_soc=True,
        soc_tolerance=0,  # ±1%容差
        population_size=300,  # 种群大小
        n_generations=500,   # 进化代数
        crossover_prob=0.7,  # 交叉概率
        mutation_prob=0.15    # 变异概率
    )

    method1_time = time.time() - method1_start

    print(f"\n=== GA优化结果（方案1）===")
    print(f"使用实际SOC模式 MAE: {best_mae_actual:.4f} V")
    print(f"总运行时间: {method1_time:.2f} 秒 ({method1_time/60:.1f} 分钟)")

    # 可选：对比原始固定范围方法
    print(f"\n--- 对比：使用固定范围 ---")

    method2_start = time.time()

    best_params_fixed, best_mae_fixed = optimizer_ga.optimize_parameters(
        use_actual_soc=False,
        population_size=300,
        n_generations=500,
        crossover_prob=0.7,
        mutation_prob=0.15
    )

    method2_time = time.time() - method2_start

    print(f"\n=== GA优化结果（方案2）===")
    print(f"固定范围模式 MAE: {best_mae_fixed:.4f} V")
    print(f"总运行时间: {method2_time:.2f} 秒 ({method2_time/60:.1f} 分钟)")

    # 结果对比
    improvement = (best_mae_fixed - best_mae_actual) / best_mae_fixed * 100
    total_time_both = method1_time + method2_time

    print(f"\n" + "="*60)
    print("📊 GA方法对比总结")
    print("="*60)
    print(f"实际SOC方法: MAE={best_mae_actual:.4f}V, 用时={method1_time:.1f}s")
    print(f"固定范围方法: MAE={best_mae_fixed:.4f}V, 用时={method2_time:.1f}s")
    print(f"总运行时间: {total_time_both:.2f}s ({total_time_both/60:.1f}分钟)")

    if improvement > 0:
        print(f"✅ 精度提升: {improvement:.2f}%")
    else:
        print(f"⚠️  精度变化: {improvement:.2f}%")

    # 效率对比
    efficiency1 = 1 / (best_mae_actual * method1_time)  # 精度/时间比
    efficiency2 = 1 / (best_mae_fixed * method2_time)

    if efficiency1 > efficiency2:
        print(f"🏆 实际SOC方法效率更高")
    else:
        print(f"🏆 固定范围方法效率更高")

    print("="*60)

    print(f"\n=== GA算法特点（CUDA加速版）===")
    print("- 使用DEAP库实现遗传算法")
    print("- 混合交叉 + 有界高斯变异")
    print("- 锦标赛选择策略")
    print("- CUDA GPU并行计算加速")
    print("- CPU多线程并行评估")
    print("- 自动回退机制（GPU不可用时使用CPU）")
    print("- 保持与PSO版本相同的约束条件和目标函数")

    # 性能统计
    device_info = "GPU (CUDA)" if optimizer_ga.use_cuda else f"CPU ({optimizer_ga.n_workers}线程)"
    print(f"- 计算设备: {device_info}")
    print(f"- CUDA可用性: {'✓' if CUDA_AVAILABLE else '✗'}")
