#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ECM电池参数优化器配置文件

包含所有超参数和配置选项
"""

import numpy as np
import multiprocessing as mp

class Config:
    """配置类，包含所有超参数和设置"""
    
    # ==================== 电池模型参数 ====================
    
    # SOC采样点 (0%-100%)
    SOC_POINTS = np.array([
        0.0, 1.0, 2.0, 3.0, 5.0, 7.0, 10.0, 15.0, 20.0,   # 放电截止处密集 (0-20%)
        30.0, 40.0, 50.0, 60.0, 70.0, 80.0,               # 平台期稀疏 (20-80%)
        85.0, 90.0, 93.0, 95.0, 97.0, 98.0, 99.0, 100.0   # 充电截止处密集 (80-100%)
    ])
    
    # 固定内阻值 (Ω)
    FIXED_R0 = 0.0006
    
    # 电池容量 (Ah)
    BATTERY_CAPACITY = 280
    
    # 默认OCV曲线
    DEFAULT_OCV = np.array([
        2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 2.95, 3.10, 3.20,  # 对应放电截止的电压
        3.28, 3.30, 3.31, 3.32, 3.33, 3.34,                    # 对应平台期的电压
        3.36, 3.38, 3.40, 3.41, 3.42, 3.43, 3.44, 3.45         # 对应充电截止的电压
    ])
    
    # OCV允许波动范围 (±V)
    OCV_VARIATION_RANGE = 0.3
    
    # ==================== 遗传算法参数 ====================
    
    # 种群大小
    POPULATION_SIZE = 300
    
    # 进化代数
    N_GENERATIONS = 400
    
    # 交叉概率
    CROSSOVER_PROB = 0.7
    
    # 变异概率
    MUTATION_PROB = 0.15
    
    # 变异强度 (参数范围的百分比)
    MUTATION_STRENGTH = 0.1
    
    # 锦标赛选择大小
    TOURNAMENT_SIZE = 3
    
    # 混合交叉参数
    BLEND_ALPHA = 0.3
    
    # ==================== SOC参数 ====================
    
    # 是否使用实际SOC数据确定初始SOC范围
    USE_ACTUAL_SOC = True
    
    # SOC容差范围 (%)
    SOC_TOLERANCE = 0
    
    # 默认初始SOC范围 (%)
    DEFAULT_SOC_RANGE = (5, 6)
    
    # ==================== 约束参数 ====================
    
    # OCV边界违反惩罚系数
    OCV_BOUNDARY_PENALTY = 100
    
    # OCV单调性违反惩罚系数
    OCV_MONOTONIC_PENALTY = 0.05
    
    # ==================== 并行计算参数 ====================
    
    # 最大并行文件数 (None表示自动检测)
    MAX_PARALLEL_FILES = None
    
    # 每个进程的内部线程数
    N_WORKERS_PER_PROCESS = 4
    
    # 最大CPU工作线程数
    MAX_CPU_WORKERS = 8
    
    # ==================== GPU参数 ====================
    
    # GPU批处理大小
    GPU_BATCH_SIZE = 1024
    
    # CPU批处理大小
    CPU_BATCH_SIZE = 300
    
    # GPU内存池限制 (GB)
    GPU_MEMORY_LIMIT = 20
    
    # ==================== 输出控制参数 ====================
    
    # 进度显示间隔 (代数)
    PROGRESS_DISPLAY_INTERVAL = 10
    
    # 详细信息显示间隔 (代数)
    DETAILED_INFO_INTERVAL = 50
    
    # 结果保存间隔 (文件数)
    RESULT_SAVE_INTERVAL = 3
    
    # 图表DPI
    PLOT_DPI = 300
    
    # 图表尺寸
    COMPARISON_PLOT_SIZE = (12, 10)
    OPTIMIZATION_PLOT_SIZE = (15, 5)
    
    # ==================== 文件处理参数 ====================
    
    # 默认文件模式
    DEFAULT_FILE_PATTERN = "*.h5"
    
    # 默认站点_堆_簇
    DEFAULT_STATION_STACK_CLUSTER = "127_1_1"
    
    # 列名匹配模式
    CURRENT_COL_PATTERN = "电流"
    VOLTAGE_COL_PATTERN = "电压"
    
    # ==================== 动态计算属性 ====================
    
    @classmethod
    def get_ocv_bounds(cls):
        """获取OCV边界"""
        return (cls.DEFAULT_OCV - cls.OCV_VARIATION_RANGE, 
                cls.DEFAULT_OCV + cls.OCV_VARIATION_RANGE)
    
    @classmethod
    def get_max_parallel_files(cls):
        """获取最大并行文件数"""
        if cls.MAX_PARALLEL_FILES is None:
            return min(mp.cpu_count() // 2, 4)
        return cls.MAX_PARALLEL_FILES
    
    @classmethod
    def get_gpu_memory_limit_bytes(cls):
        """获取GPU内存限制（字节）"""
        return cls.GPU_MEMORY_LIMIT * 1024**3
    
    @classmethod
    def get_max_cpu_workers(cls):
        """获取最大CPU工作线程数"""
        return min(mp.cpu_count(), cls.MAX_CPU_WORKERS)
    
    @classmethod
    def validate_config(cls):
        """验证配置参数的有效性"""
        assert len(cls.SOC_POINTS) == len(cls.DEFAULT_OCV), "SOC点和OCV点长度必须一致"
        assert cls.POPULATION_SIZE > 0, "种群大小必须大于0"
        assert cls.N_GENERATIONS > 0, "进化代数必须大于0"
        assert 0 <= cls.CROSSOVER_PROB <= 1, "交叉概率必须在0-1之间"
        assert 0 <= cls.MUTATION_PROB <= 1, "变异概率必须在0-1之间"
        assert cls.FIXED_R0 > 0, "内阻值必须大于0"
        assert cls.BATTERY_CAPACITY > 0, "电池容量必须大于0"
        print("✓ 配置参数验证通过")

# 验证配置
Config.validate_config()
