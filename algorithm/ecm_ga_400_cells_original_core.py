#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
400电芯ECM参数辨识器 - 使用原始核心算法

主要功能:
- 保持400电芯循环处理逻辑
- 核心参数识别完全使用原始代码
- 确保结果格式和精度与原始版本一致
- 支持GPU/CPU混合加速计算

作者: 基于ecm_ga_multi_day_optimizer.py核心算法
日期: 2025-08-08
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import warnings
import os
import glob
import json
from datetime import datetime
import re
import openpyxl
warnings.filterwarnings('ignore')

# 导入原始优化器核心类
from ecm_ga_multi_day_optimizer import BatteryParameterOptimizerGA

# 遗传算法相关导入
from deap import base, creator, tools
import random

# 配置和字体设置
from config import Config
from font_config import setup_chinese_font

# 初始化配置和字体
setup_chinese_font(verbose=True)


class BatteryParameterOptimizerGA_EarlyStop(BatteryParameterOptimizerGA):
    """带早停功能的电池参数优化器"""

    def optimize_parameters_with_early_stop(self, use_actual_soc=None, soc_tolerance=None,
                                           population_size=None, n_generations=None,
                                           crossover_prob=None, mutation_prob=None,
                                           early_stop_patience=50, early_stop_threshold=1e-6,
                                           plot_output_dir=None, file_date=None):
        """
        运行遗传算法参数优化（带早停功能）

        参数:
            early_stop_patience: 早停耐心值，连续多少代没有改善就停止
            early_stop_threshold: 早停阈值，改善小于此值认为没有改善
            其他参数同原始版本
        """
        # 使用配置文件中的默认值
        use_actual_soc = use_actual_soc if use_actual_soc is not None else Config.USE_ACTUAL_SOC
        soc_tolerance = soc_tolerance if soc_tolerance is not None else Config.SOC_TOLERANCE
        population_size = population_size if population_size is not None else Config.POPULATION_SIZE
        n_generations = n_generations if n_generations is not None else Config.N_GENERATIONS
        crossover_prob = crossover_prob if crossover_prob is not None else Config.CROSSOVER_PROB
        mutation_prob = mutation_prob if mutation_prob is not None else Config.MUTATION_PROB

        if use_actual_soc:
            # 使用实际SOC数据确定初始SOC范围
            soc_cols = [col for col in self.data.columns if 'SOC' in col]

            if soc_cols:
                soc_col_name = soc_cols[0]
                actual_initial_soc = self.data[soc_col_name].iloc[0]
                soc_lower = max(0, actual_initial_soc - soc_tolerance)
                soc_upper = min(100, actual_initial_soc + soc_tolerance)
                print(f"使用实际SOC数据: 初始SOC={actual_initial_soc:.2f}%, 搜索范围=[{soc_lower:.2f}%, {soc_upper:.2f}%]")
            else:
                soc_lower, soc_upper = Config.DEFAULT_SOC_RANGE
                print(f"未找到SOC列，使用默认范围: [{soc_lower}%, {soc_upper}%]")
        else:
            soc_lower, soc_upper = Config.DEFAULT_SOC_RANGE
            print(f"使用默认SOC范围: [{soc_lower}%, {soc_upper}%]")

        # 设定参数边界：[初始SOC] + [23个OCV点]
        self.lb = [soc_lower] + list(self.ocv_bounds[0])
        self.ub = [soc_upper] + list(self.ocv_bounds[1])

        # 设置遗传算法
        self._setup_ga(population_size)

        print(f"开始遗传算法优化...")
        print(f"种群大小: {population_size}, 进化代数: {n_generations}")
        print(f"早停设置: 耐心值={early_stop_patience}, 阈值={early_stop_threshold}")

        start_time = time.time()

        # 创建初始种群
        population = self.toolbox.population(n=population_size)

        # 并行评估初始种群
        fitnesses = self._parallel_evaluate(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit

        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)

        # 早停相关变量
        best_fitness_history = []
        no_improvement_count = 0
        best_ever_fitness = float('inf')

        # 进化过程
        generation_start_time = time.time()
        actual_generations = 0

        for generation in range(n_generations):
            actual_generations = generation + 1
            gen_start = time.time()

            # 选择下一代
            offspring = self.toolbox.select(population, len(population))
            offspring = list(map(self.toolbox.clone, offspring))

            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < crossover_prob:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values

            for mutant in offspring:
                if random.random() < mutation_prob:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values

            # 并行评估需要重新评估的个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            if invalid_ind:
                fitnesses = self._parallel_evaluate(invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit

            # 替换种群
            population[:] = offspring

            # 记录最佳个体
            best_ind = tools.selBest(population, 1)[0]
            best_fitness = best_ind.fitness.values[0]

            # 更新历史记录
            self.optimization_history['generation'].append(generation + 1)
            self.optimization_history['best_fitness'].append(best_fitness)
            self.optimization_history['best_ocv'].append(best_ind[1:].copy())
            best_fitness_history.append(best_fitness)

            # 早停检查
            if best_fitness < best_ever_fitness - early_stop_threshold:
                best_ever_fitness = best_fitness
                no_improvement_count = 0
            else:
                no_improvement_count += 1

            # 打印进度（包含早停信息）
            gen_time = time.time() - gen_start
            elapsed_time = time.time() - generation_start_time

            if (generation + 1) % Config.PROGRESS_DISPLAY_INTERVAL == 0:
                record = stats.compile(population)
                avg_gen_time = elapsed_time / (generation + 1)
                eta = avg_gen_time * (n_generations - generation - 1)

                print(f"代数 {generation + 1}/{n_generations}: "
                      f"最佳={best_fitness:.6f}, 平均={record['avg']:.6f}, "
                      f"本代用时={gen_time:.2f}s, 已用时={elapsed_time:.1f}s, "
                      f"预计剩余={eta:.1f}s, 无改善={no_improvement_count}/{early_stop_patience}")
            elif (generation + 1) % Config.DETAILED_INFO_INTERVAL == 0:
                record = stats.compile(population)
                progress = (generation + 1) / n_generations * 100
                print(f"进度 {progress:.1f}%: 最佳适应度={best_fitness:.6f}, "
                      f"初始SOC={best_ind[0]:.2f}%, 已用时={elapsed_time:.1f}s, "
                      f"无改善={no_improvement_count}/{early_stop_patience}")

            # 早停判断
            if no_improvement_count >= early_stop_patience:
                print(f"\n🛑 早停触发: 连续{early_stop_patience}代无改善(阈值={early_stop_threshold})")
                print(f"在第{generation + 1}代停止，最佳适应度={best_fitness:.6f}")
                break

        # 获取最优解
        best_individual = tools.selBest(population, 1)[0]
        best_params = list(best_individual)
        best_mae = best_individual.fitness.values[0]

        total_time = time.time() - start_time

        print(f"\n优化完成!")
        print(f"实际运行代数: {actual_generations}/{n_generations}")
        print(f"最佳MAE: {best_mae:.6f}")
        print(f"初始SOC: {best_params[0]:.2f}%")
        print(f"总用时: {total_time:.2f}秒")
        if actual_generations < n_generations:
            print(f"早停节省时间: {(n_generations - actual_generations) / n_generations * 100:.1f}%")

        # 生成图表（如果指定了输出目录）
        if plot_output_dir and file_date:
            self._generate_cell_plots(best_params, plot_output_dir, file_date, self.voltage_col)

        return {
            'best_params': best_params,
            'best_mae': best_mae,
            'initial_soc': best_params[0],
            'ocv_points': best_params[1:],
            'optimization_time': total_time,
            'final_generation': actual_generations,
            'fitness_history': best_fitness_history,
            'early_stopped': actual_generations < n_generations,
            'no_improvement_count': no_improvement_count
        }

    def _generate_cell_plots(self, best_params, plot_output_dir, file_date, voltage_col):
        """生成电芯相关图表"""
        os.makedirs(plot_output_dir, exist_ok=True)

        # 提取电芯编号
        cell_num = voltage_col.split('电压')[-1] if '电压' in voltage_col else 'unknown'

        # 1. 生成对比图
        comparison_filename = os.path.join(plot_output_dir,
                                         f"comparison_{file_date}_{cell_num}.png")
        self._plot_comparison(best_params, comparison_filename, voltage_col)

        # 2. 生成优化过程图
        optimization_filename = os.path.join(plot_output_dir,
                                           f"optimization_{file_date}_{cell_num}.png")
        self._plot_optimization_process(optimization_filename)

    def _plot_comparison(self, params, plot_filename=None, voltage_col=None):
        """绘制电芯对比图"""
        # 使用最佳参数进行模拟
        initial_soc = params[0]
        ocv_points = params[1:]
        capacity_Ah = Config.BATTERY_CAPACITY

        # 使用传入的voltage_col或者self.voltage_col
        voltage_column = voltage_col or self.voltage_col

        # 创建OCV插值函数
        from scipy.interpolate import interp1d
        ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear', fill_value="extrapolate")

        # 计算时间差
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')

        # 计算SOC
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / capacity_Ah * 100
        sim_soc = initial_soc - np.cumsum(delta_soc)
        sim_soc = np.clip(sim_soc, 0, 100)

        # 计算模拟电压
        sim_voltage = ocv_interp(sim_soc) - current_values * self.fixed_r0

        plt.figure(figsize=Config.COMPARISON_PLOT_SIZE)

        # 电压对比
        plt.subplot(3, 1, 1)
        plt.plot(self.data.index, self.data[voltage_column], 'b-', label='真实电压')
        plt.plot(self.data.index, sim_voltage, 'r--', label='模拟电压')
        plt.ylabel('电压(V)')
        cell_num = voltage_column.split('电压')[-1] if '电压' in voltage_column else 'Unknown'
        plt.title(f'电芯{cell_num}模拟 (容量={capacity_Ah}Ah, 初始SOC={initial_soc:.2f}%, 固定R0={self.fixed_r0}Ω)')
        plt.legend()
        plt.grid(True)

        # SOC变化
        plt.subplot(3, 1, 2)
        plt.plot(self.data.index, sim_soc, 'g-', label='模拟SOC')
        # 检查SOC列
        soc_col_candidates = [col for col in self.data.columns if 'SOC' in col]
        if soc_col_candidates:
            bms_soc_col = soc_col_candidates[0]
            plt.plot(self.data.index, self.data[bms_soc_col], 'r-', label='BMS_SOC')
        plt.ylabel('SOC(%)')
        plt.legend()
        plt.grid(True)

        # OCV变化
        plt.subplot(3, 1, 3)
        plt.plot(self.data.index, ocv_interp(sim_soc), 'm-', label='OCV')
        plt.xlabel('时间')
        plt.ylabel('OCV(V)')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if plot_filename:
            plt.savefig(plot_filename, dpi=Config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            print(f"对比图已保存: {plot_filename}")
        else:
            plt.show()

    def _plot_optimization_process(self, plot_filename=None):
        """绘制优化过程曲线"""
        if not self.optimization_history['generation']:
            return

        plt.figure(figsize=Config.OPTIMIZATION_PLOT_SIZE)

        # 1. 适应度变化曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.optimization_history['generation'],
                self.optimization_history['best_fitness'], 'b-')
        plt.xlabel('代数')
        plt.ylabel('目标函数值')
        plt.title('GA优化过程收敛曲线')
        plt.grid(True)

        # 2. OCV曲线进化过程
        plt.subplot(1, 2, 2)
        for i in range(0, len(self.optimization_history['generation']), 10):
            plt.plot(self.soc_points, self.optimization_history['best_ocv'][i],
                    alpha=0.1, color='blue')
        plt.plot(self.soc_points, self.optimization_history['best_ocv'][-1],
                'r-', linewidth=2, label='最终OCV曲线')
        plt.plot(self.soc_points, self.default_ocv, 'g--', label='默认OCV曲线')
        plt.fill_between(self.soc_points,
                        self.ocv_bounds[0],
                        self.ocv_bounds[1],
                        color='gray', alpha=0.2, label='允许波动范围')
        plt.xlabel('SOC(%)')
        plt.ylabel('OCV(V)')
        plt.title('OCV曲线优化过程')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if plot_filename:
            plt.savefig(plot_filename, dpi=Config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            print(f"优化过程图已保存: {plot_filename}")
        else:
            plt.show()


class Battery400CellsOriginalCore:
    """400电芯ECM参数优化器 - 使用原始核心算法"""

    def __init__(self, data_dir="../database", output_base_dir="./127_station_results"):
        self.data_dir = data_dir
        self.output_base_dir = output_base_dir

        # 从配置文件加载参数
        self.soc_points = Config.SOC_POINTS
        self.fixed_r0 = Config.FIXED_R0
        self.default_ocv = Config.DEFAULT_OCV

        # 电站配置
        self.station_id = "127"
        self.stacks = [1, 2]  # 1号堆、2号堆
        self.clusters_per_stack = 7  # 每个堆7个簇

        print(f"400电芯ECM参数辨识器已初始化")
        print(f"数据目录: {self.data_dir}")
        print(f"输出目录: {self.output_base_dir}")
        print(f"电站配置: {self.station_id}站, {len(self.stacks)}个堆, 每堆{self.clusters_per_stack}个簇")

    def _detect_all_voltage_columns(self, data):
        """检测所有电芯电压列"""
        voltage_cols = []
        for col in data.columns:
            if '单体电压' in col:
                # 提取电芯编号
                match = re.search(r'单体电压(\d+)', col)
                if match:
                    cell_num = int(match.group(1))
                    voltage_cols.append((cell_num, col))
        
        # 按电芯编号排序
        voltage_cols.sort(key=lambda x: x[0])
        return [col for _, col in voltage_cols]

    def _detect_current_column(self, data):
        """检测电流列"""
        current_candidates = [col for col in data.columns if '电流' in col or 'current' in col.lower()]
        return current_candidates[0] if current_candidates else None

    def optimize_single_cell_original(self, data, current_col, voltage_col,
                                    use_actual_soc=True, soc_tolerance=0,
                                    population_size=300, n_generations=400,
                                    crossover_prob=0.7, mutation_prob=0.15,
                                    early_stop_patience=50, early_stop_threshold=1e-6,
                                    plot_output_dir=None, file_date=None):
        """
        使用原始算法优化单个电芯
        
        参数:
            data: 数据DataFrame
            current_col: 电流列名
            voltage_col: 电压列名
            其他参数: 遗传算法参数
        
        返回:
            dict: 包含完整优化结果的字典（与原始版本格式一致）
        """
        print(f"开始优化电芯: {voltage_col}")
        
        try:
            # 创建带早停功能的优化器实例
            optimizer = BatteryParameterOptimizerGA_EarlyStop(
                data=data,
                current_col=current_col,
                voltage_col=voltage_col,
                use_cuda=True,  # 启用CUDA加速
                n_workers=4
            )

            # 运行优化（使用带早停功能的算法）
            result = optimizer.optimize_parameters_with_early_stop(
                use_actual_soc=use_actual_soc,
                soc_tolerance=soc_tolerance,
                population_size=population_size,
                n_generations=n_generations,
                crossover_prob=crossover_prob,
                mutation_prob=mutation_prob,
                early_stop_patience=early_stop_patience,
                early_stop_threshold=early_stop_threshold,
                plot_output_dir=plot_output_dir,  # 启用图表生成
                file_date=file_date
            )
            
            # 提取电芯编号
            cell_match = re.search(r'单体电压(\d+)', voltage_col)
            cell_index = int(cell_match.group(1)) if cell_match else 0
            
            # 构建与原始版本一致的结果格式（添加早停信息）
            formatted_result = {
                'cell_index': cell_index,
                'voltage_col': voltage_col,
                'current_col': current_col,
                'best_mae': result['best_mae'],
                'initial_soc': result['initial_soc'],
                'ocv_points': result['ocv_points'],  # 完整的23个OCV点
                'soc_points': self.soc_points.tolist(),  # SOC采样点
                'optimization_time': result['optimization_time'],
                'final_generation': result.get('final_generation', n_generations),
                'best_params': result['best_params'],
                'fitness_history': result.get('fitness_history', []),
                'data_shape': data.shape,
                'time_range_start': str(data.index[0]) if len(data) > 0 else None,
                'time_range_end': str(data.index[-1]) if len(data) > 0 else None,
                'processing_timestamp': datetime.now().isoformat(),
                'gpu_used': True,
                'status': 'success',
                # 早停相关信息
                'early_stopped': result.get('early_stopped', False),
                'no_improvement_count': result.get('no_improvement_count', 0),
                'early_stop_patience': early_stop_patience,
                'early_stop_threshold': early_stop_threshold
            }
            
            print(f"电芯{cell_index}优化完成: MAE = {result['best_mae']:.6f}, 用时 {result['optimization_time']:.1f}s")
            return formatted_result
            
        except Exception as e:
            print(f"电芯优化失败: {e}")
            # 提取电芯编号
            cell_match = re.search(r'单体电压(\d+)', voltage_col)
            cell_index = int(cell_match.group(1)) if cell_match else 0
            
            return {
                'cell_index': cell_index,
                'voltage_col': voltage_col,
                'current_col': current_col,
                'status': 'failed',
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat()
            }

    def process_single_file_400_cells(self, file_path, 
                                    start_cell=1, end_cell=400,
                                    optimization_params=None):
        """
        处理单个文件的400电芯参数辨识
        
        参数:
            file_path: HDF5文件路径
            start_cell: 起始电芯编号
            end_cell: 结束电芯编号
            optimization_params: 优化参数字典
        
        返回:
            dict: 处理结果
        """
        # 默认优化参数（使用原始版本的参数）
        if optimization_params is None:
            optimization_params = {
                'use_actual_soc': Config.USE_ACTUAL_SOC,
                'soc_tolerance': Config.SOC_TOLERANCE,
                'population_size': Config.POPULATION_SIZE,  # 300
                'n_generations': Config.N_GENERATIONS,      # 400
                'crossover_prob': Config.CROSSOVER_PROB,    # 0.7
                'mutation_prob': Config.MUTATION_PROB,      # 0.15
                'save_interval': 10
            }
        
        def _extract_date_from_filename(filename):
            """从文件名提取日期信息"""
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
            if date_match:
                return date_match.group(1)
            date_match = re.search(r'(\d{8})', filename)
            if date_match:
                date_str = date_match.group(1)
                return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            return os.path.splitext(filename)[0]
        
        file_name = os.path.basename(file_path)
        file_date = _extract_date_from_filename(file_name)
        
        # 创建文件专用输出目录
        file_output_dir = os.path.join(self.output_base_dir, f"{file_date}_400_cells_original")
        os.makedirs(file_output_dir, exist_ok=True)

        # 创建图表输出目录
        plot_output_dir = os.path.join(file_output_dir, "plots")
        os.makedirs(plot_output_dir, exist_ok=True)
        
        try:
            print(f"\n{'='*80}")
            print(f"开始处理文件: {file_name}")
            print(f"使用原始核心算法")
            print(f"{'='*80}")
            
            # 加载数据
            data = pd.read_hdf(file_path)
            print(f"数据形状: {data.shape}")
            
            # 检测电流列
            current_col = self._detect_current_column(data)
            if not current_col:
                raise ValueError(f"无法检测到电流列。可用列: {list(data.columns)}")
            
            print(f"检测到电流列: {current_col}")
            
            # 检测所有电芯电压列
            voltage_cols = self._detect_all_voltage_columns(data)
            print(f"检测到 {len(voltage_cols)} 个电芯电压列")
            
            # 获取要处理的电芯列表
            cells_to_process = []
            for i in range(start_cell, min(end_cell + 1, len(voltage_cols) + 1)):
                if i <= len(voltage_cols):
                    voltage_col = voltage_cols[i-1]  # 索引从0开始
                    cells_to_process.append((i, voltage_col))
            
            print(f"实际处理电芯范围: {start_cell} - {min(end_cell, len(voltage_cols))} ({len(cells_to_process)}个电芯)")
            print(f"优化参数: 种群={optimization_params['population_size']}, 代数={optimization_params['n_generations']}")
            
            # 处理统计
            processing_stats = {
                'total_cells': len(cells_to_process),
                'processed_cells': 0,
                'successful_cells': 0,
                'failed_cells': 0,
                'start_time': time.time()
            }
            
            all_results = {}
            
            # 开始循环处理每个电芯
            for cell_idx, (cell_num, voltage_col) in enumerate(cells_to_process):
                try:
                    # 使用带早停功能的算法优化单个电芯
                    result = self.optimize_single_cell_original(
                        data=data,
                        current_col=current_col,
                        voltage_col=voltage_col,
                        use_actual_soc=optimization_params.get('use_actual_soc', True),
                        soc_tolerance=optimization_params.get('soc_tolerance', 0),
                        population_size=optimization_params.get('population_size', 300),
                        n_generations=optimization_params.get('n_generations', 400),
                        crossover_prob=optimization_params.get('crossover_prob', 0.7),
                        mutation_prob=optimization_params.get('mutation_prob', 0.15),
                        early_stop_patience=optimization_params.get('early_stop_patience', 50),
                        early_stop_threshold=optimization_params.get('early_stop_threshold', 1e-6),
                        plot_output_dir=plot_output_dir,  # 传递图表输出目录
                        file_date=file_date               # 传递文件日期
                    )
                    
                    # 保存结果
                    all_results[cell_num] = result
                    if result['status'] == 'success':
                        processing_stats['successful_cells'] += 1
                    else:
                        processing_stats['failed_cells'] += 1
                    
                    processing_stats['processed_cells'] += 1
                    
                    # 显示进度
                    elapsed_time = time.time() - processing_stats['start_time']
                    avg_time_per_cell = elapsed_time / processing_stats['processed_cells']
                    remaining_cells = len(cells_to_process) - processing_stats['processed_cells']
                    estimated_remaining_time = avg_time_per_cell * remaining_cells
                    
                    progress = processing_stats['processed_cells'] / len(cells_to_process) * 100
                    print(f"\n进度: {progress:.1f}% ({processing_stats['processed_cells']}/{len(cells_to_process)})")
                    print(f"已用时间: {elapsed_time:.1f}s, 预计剩余: {estimated_remaining_time:.1f}s")
                    print(f"成功: {processing_stats['successful_cells']}, 失败: {processing_stats['failed_cells']}")
                    
                    # 定期保存结果
                    if processing_stats['processed_cells'] % optimization_params.get('save_interval', 10) == 0:
                        self._save_intermediate_results(all_results, file_output_dir, processing_stats)
                
                except Exception as e:
                    print(f"电芯 {cell_num} 处理失败: {e}")
                    processing_stats['failed_cells'] += 1
                    processing_stats['processed_cells'] += 1
                    
                    # 记录失败信息
                    all_results[cell_num] = {
                        'cell_index': cell_num,
                        'voltage_col': voltage_col,
                        'current_col': current_col,
                        'status': 'failed',
                        'error': str(e),
                        'processing_timestamp': datetime.now().isoformat()
                    }
            
            # 最终保存
            processing_stats['total_time'] = time.time() - processing_stats['start_time']
            self._save_final_results(all_results, file_output_dir, processing_stats, file_name, file_date)
            
            print(f"\n{'='*80}")
            print(f"400电芯ECM参数辨识完成!")
            print(f"{'='*80}")
            print(f"总用时: {processing_stats['total_time']:.1f}s")
            print(f"成功处理: {processing_stats['successful_cells']} 个电芯")
            print(f"处理失败: {processing_stats['failed_cells']} 个电芯")
            print(f"结果保存在: {file_output_dir}")
            print(f"{'='*80}")
            
            return {
                'file_name': file_name,
                'file_date': file_date,
                'status': 'success',
                'total_cells': len(all_results),
                'successful_cells': processing_stats['successful_cells'],
                'failed_cells': processing_stats['failed_cells'],
                'output_dir': file_output_dir,
                'processing_timestamp': datetime.now().isoformat(),
                'total_time': processing_stats['total_time']
            }
            
        except Exception as e:
            print(f"处理文件 {file_name} 时发生错误: {e}")
            return {
                'file_name': file_name,
                'file_date': file_date,
                'status': 'failed',
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat()
            }

    def _save_intermediate_results(self, results, output_dir, stats):
        """保存中间结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON格式结果
        json_file = os.path.join(output_dir, f"intermediate_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_results = {}
            for cell_num, result in results.items():
                if 'best_params' in result:
                    json_result = result.copy()
                    json_result['best_params'] = [float(x) for x in result['best_params']]
                    json_result['ocv_points'] = [float(x) for x in result['ocv_points']]
                    if 'fitness_history' in result:
                        json_result['fitness_history'] = [float(x) for x in result['fitness_history']]
                    json_results[str(cell_num)] = json_result
                else:
                    json_results[str(cell_num)] = result

            json.dump({
                'results': json_results,
                'statistics': stats,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)

        print(f"中间结果已保存: {json_file}")

    def _save_final_results(self, results, output_dir, stats, file_name, file_date):
        """保存最终结果（与原始版本格式一致）"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 1. 保存完整JSON结果
        json_file = os.path.join(output_dir, f"400_cells_complete_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json_results = {}
            for cell_num, result in results.items():
                if 'best_params' in result:
                    json_result = result.copy()
                    json_result['best_params'] = [float(x) for x in result['best_params']]
                    json_result['ocv_points'] = [float(x) for x in result['ocv_points']]
                    if 'fitness_history' in result:
                        json_result['fitness_history'] = [float(x) for x in result['fitness_history']]
                    json_results[str(cell_num)] = json_result
                else:
                    json_results[str(cell_num)] = result

            json.dump({
                'results': json_results,
                'statistics': stats,
                'metadata': {
                    'file_name': file_name,
                    'file_date': file_date,
                    'total_cells': len(results),
                    'successful_cells': stats['successful_cells'],
                    'failed_cells': stats['failed_cells'],
                    'processing_date': timestamp,
                    'algorithm_config': {
                        'soc_points': len(self.soc_points),
                        'fixed_r0': self.fixed_r0,
                        'battery_capacity': Config.BATTERY_CAPACITY,
                        'cuda_enabled': True
                    }
                }
            }, f, indent=2, ensure_ascii=False)

        # 2. 保存CSV汇总结果（与原始版本格式一致）
        csv_file = os.path.join(output_dir, f"400_cells_summary_{timestamp}.csv")
        summary_data = []

        for cell_num, result in results.items():
            if result['status'] == 'success':
                # 格式化OCV点为字符串（与原始版本一致）
                ocv_points_str = str(result['ocv_points'])

                summary_data.append({
                    'cell_index': result['cell_index'],
                    'file_name': file_name,
                    'date': file_date,
                    'best_mae': result['best_mae'],
                    'initial_soc': result['initial_soc'],
                    'ocv_points': ocv_points_str,
                    'total_time_seconds': result['optimization_time'],
                    'current_col_used': result['current_col'],
                    'voltage_col_used': result['voltage_col'],
                    'data_shape': f"{result['data_shape'][0]}x{result['data_shape'][1]}",
                    'time_range_start': result['time_range_start'],
                    'time_range_end': result['time_range_end'],
                    'processing_timestamp': result['processing_timestamp'],
                    'gpu_used': result['gpu_used'],
                    'status': result['status'],
                    # 早停相关信息
                    'final_generation': result.get('final_generation', 0),
                    'early_stopped': result.get('early_stopped', False),
                    'no_improvement_count': result.get('no_improvement_count', 0),
                    'early_stop_patience': result.get('early_stop_patience', 0),
                    'early_stop_threshold': result.get('early_stop_threshold', 0)
                })
            else:
                summary_data.append({
                    'cell_index': result['cell_index'],
                    'file_name': file_name,
                    'date': file_date,
                    'best_mae': None,
                    'initial_soc': None,
                    'ocv_points': None,
                    'total_time_seconds': None,
                    'current_col_used': result.get('current_col', ''),
                    'voltage_col_used': result['voltage_col'],
                    'data_shape': None,
                    'time_range_start': None,
                    'time_range_end': None,
                    'processing_timestamp': result['processing_timestamp'],
                    'gpu_used': False,
                    'status': 'failed',
                    'error': result.get('error', 'Unknown error')
                })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(csv_file, index=False, encoding='utf-8-sig')

        print(f"最终结果已保存:")
        print(f"  完整结果: {json_file}")
        print(f"  汇总结果: {csv_file}")

    def process_station_all_clusters(self, date="2023-12-25", optimization_params=None):
        """
        处理整个电站一天内所有簇的数据

        参数:
            date: 处理日期
            optimization_params: 优化参数字典

        返回:
            dict: 全站处理结果
        """
        print(f"\n{'='*80}")
        print(f"开始处理{self.station_id}电站{date}的所有簇数据")
        print(f"{'='*80}")

        # 创建全站输出目录
        station_output_dir = os.path.join(self.output_base_dir, f"{self.station_id}_station_{date}_results")
        os.makedirs(station_output_dir, exist_ok=True)

        # 初始化处理统计
        station_stats = {
            'station_id': self.station_id,
            'date': date,
            'total_files': len(self.stacks) * self.clusters_per_stack,
            'processed_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_cells': 0,
            'successful_cells': 0,
            'failed_cells': 0,
            'start_time': time.time(),
            'file_results': [],
            'stack_results': {}
        }

        # 处理每个堆
        for stack_id in self.stacks:
            print(f"\n{'='*60}")
            print(f"开始处理{stack_id}号堆")
            print(f"{'='*60}")

            stack_output_dir = os.path.join(station_output_dir, f"stack_{stack_id}")
            os.makedirs(stack_output_dir, exist_ok=True)

            stack_stats = {
                'stack_id': stack_id,
                'total_clusters': self.clusters_per_stack,
                'processed_clusters': 0,
                'successful_clusters': 0,
                'failed_clusters': 0,
                'total_cells': 0,
                'successful_cells': 0,
                'failed_cells': 0,
                'cluster_results': {}
            }

            # 处理每个簇
            for cluster_id in range(1, self.clusters_per_stack + 1):
                file_name = f"{self.station_id}_{stack_id}_{cluster_id}_{date}.h5"
                file_path = os.path.join(self.data_dir, file_name)

                print(f"\n{'='*40}")
                print(f"处理文件 {station_stats['processed_files']+1}/{station_stats['total_files']}: {file_name}")
                print(f"堆{stack_id}簇{cluster_id}")
                print(f"{'='*40}")

                if not os.path.exists(file_path):
                    print(f"⚠️  文件不存在: {file_path}")
                    station_stats['failed_files'] += 1
                    stack_stats['failed_clusters'] += 1
                    station_stats['processed_files'] += 1
                    stack_stats['processed_clusters'] += 1
                    continue

                try:
                    # 创建簇输出目录
                    cluster_output_dir = os.path.join(stack_output_dir, f"cluster_{cluster_id}")

                    # 处理单个簇文件
                    cluster_result = self._process_single_cluster_file(
                        file_path=file_path,
                        stack_id=stack_id,
                        cluster_id=cluster_id,
                        cluster_output_dir=cluster_output_dir,
                        optimization_params=optimization_params
                    )

                    # 更新统计信息
                    station_stats['file_results'].append(cluster_result)
                    stack_stats['cluster_results'][cluster_id] = cluster_result

                    if cluster_result['status'] == 'success':
                        station_stats['successful_files'] += 1
                        stack_stats['successful_clusters'] += 1
                        station_stats['successful_cells'] += cluster_result['successful_cells']
                        stack_stats['successful_cells'] += cluster_result['successful_cells']
                        station_stats['failed_cells'] += cluster_result['failed_cells']
                        stack_stats['failed_cells'] += cluster_result['failed_cells']
                    else:
                        station_stats['failed_files'] += 1
                        stack_stats['failed_clusters'] += 1

                    station_stats['total_cells'] += cluster_result.get('total_cells', 0)
                    stack_stats['total_cells'] += cluster_result.get('total_cells', 0)

                except Exception as e:
                    print(f"❌ 处理簇{stack_id}-{cluster_id}时发生错误: {e}")
                    station_stats['failed_files'] += 1
                    stack_stats['failed_clusters'] += 1

                station_stats['processed_files'] += 1
                stack_stats['processed_clusters'] += 1

                # 显示总体进度
                elapsed_time = time.time() - station_stats['start_time']
                progress = station_stats['processed_files'] / station_stats['total_files'] * 100
                avg_time_per_file = elapsed_time / station_stats['processed_files']
                remaining_files = station_stats['total_files'] - station_stats['processed_files']
                eta = avg_time_per_file * remaining_files

                print(f"\n📊 总体进度: {progress:.1f}% ({station_stats['processed_files']}/{station_stats['total_files']})")
                print(f"⏱️  已用时间: {elapsed_time:.1f}s, 预计剩余: {eta:.1f}s")
                print(f"✅ 成功: {station_stats['successful_files']}, ❌ 失败: {station_stats['failed_files']}")
                print(f"🔋 电芯统计: 成功{station_stats['successful_cells']}, 失败{station_stats['failed_cells']}")

            # 保存堆级汇总
            self._save_stack_summary(stack_stats, stack_output_dir)
            station_stats['stack_results'][stack_id] = stack_stats

        # 保存全站汇总
        station_stats['total_time'] = time.time() - station_stats['start_time']
        self._save_station_summary(station_stats, station_output_dir)

        print(f"\n{'='*80}")
        print(f"🎉 {self.station_id}电站{date}数据处理完成!")
        print(f"{'='*80}")
        print(f"📁 结果目录: {station_output_dir}")
        print(f"⏱️  总用时: {station_stats['total_time']:.1f}s ({station_stats['total_time']/3600:.2f}小时)")
        print(f"📊 文件统计: 成功{station_stats['successful_files']}/{station_stats['total_files']}")
        print(f"🔋 电芯统计: 成功{station_stats['successful_cells']}, 失败{station_stats['failed_cells']}")
        print(f"{'='*80}")

        return station_stats

    def _process_single_cluster_file(self, file_path, stack_id, cluster_id, cluster_output_dir, optimization_params):
        """
        处理单个簇文件的400个电芯

        参数:
            file_path: 文件路径
            stack_id: 堆编号
            cluster_id: 簇编号
            cluster_output_dir: 簇输出目录
            optimization_params: 优化参数

        返回:
            dict: 簇处理结果
        """
        os.makedirs(cluster_output_dir, exist_ok=True)

        cluster_result = {
            'stack_id': stack_id,
            'cluster_id': cluster_id,
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'start_time': time.time(),
            'status': 'processing',
            'total_cells': 400,
            'successful_cells': 0,
            'failed_cells': 0,
            'cell_results': {},
            'cluster_output_dir': cluster_output_dir
        }

        try:
            # 加载数据
            print(f"📂 加载数据文件: {os.path.basename(file_path)}")
            data = pd.read_hdf(file_path, key='data')
            print(f"📊 数据形状: {data.shape}")

            # 检测电流和电压列
            current_cols = [col for col in data.columns if '电流' in col and '簇组电流' in col]
            voltage_cols = [col for col in data.columns if '单体电压' in col and f'{cluster_id}簇' in col]

            if not current_cols:
                raise ValueError("未找到电流列")
            if not voltage_cols:
                raise ValueError(f"未找到堆{stack_id}簇{cluster_id}的电压列")

            current_col = current_cols[0]
            print(f"🔌 检测到电流列: {current_col}")
            print(f"🔋 检测到堆{stack_id}簇{cluster_id}的 {len(voltage_cols)} 个电芯电压列")

            # 检查实际可用的电芯数量
            available_cells = []
            for col in voltage_cols:
                try:
                    cell_num = int(col.split('单体电压')[-1])
                    available_cells.append(cell_num)
                except:
                    pass

            available_cells.sort()
            if available_cells:
                print(f"📊 可用电芯范围: {min(available_cells)} - {max(available_cells)} (共{len(available_cells)}个)")
            else:
                raise ValueError(f"无法解析电芯编号")

            # 处理实际存在的电芯
            cluster_result['total_cells'] = len(available_cells)

            for cell_idx in available_cells:
                # 从文件名提取日期部分，构建正确的电压列名
                file_date = os.path.basename(file_path).split('_')[3].replace('.h5', '')  # 2023-12-25
                voltage_col = f"{file_date}_{stack_id}#BMS-堆内-{stack_id}簇单体电压{cell_idx}"

                if voltage_col not in data.columns:
                    print(f"⚠️  电芯{cell_idx}电压列不存在: {voltage_col}")
                    cluster_result['failed_cells'] += 1
                    continue

                try:
                    # 创建电芯输出目录
                    cell_output_dir = os.path.join(cluster_output_dir, f"cell_{cell_idx:03d}")
                    os.makedirs(cell_output_dir, exist_ok=True)

                    print(f"🔋 处理电芯{cell_idx}/{len(available_cells)} (堆{stack_id}簇{cluster_id})")

                    # 优化单个电芯
                    cell_result = self.optimize_single_cell_original(
                        data=data,
                        current_col=current_col,
                        voltage_col=voltage_col,
                        use_actual_soc=optimization_params.get('use_actual_soc', True),
                        soc_tolerance=optimization_params.get('soc_tolerance', 0),
                        population_size=optimization_params.get('population_size', 100),
                        n_generations=optimization_params.get('n_generations', 150),
                        crossover_prob=optimization_params.get('crossover_prob', 0.7),
                        mutation_prob=optimization_params.get('mutation_prob', 0.15),
                        early_stop_patience=optimization_params.get('early_stop_patience', 30),
                        early_stop_threshold=optimization_params.get('early_stop_threshold', 1e-6),
                        plot_output_dir=cell_output_dir,
                        file_date=f"{stack_id}_{cluster_id}"
                    )

                    # 保存电芯详细结果
                    self._save_cell_detailed_results(cell_result, cell_output_dir, cell_idx, stack_id, cluster_id)

                    cluster_result['cell_results'][cell_idx] = cell_result
                    cluster_result['successful_cells'] += 1

                    print(f"✅ 电芯{cell_idx}完成: MAE={cell_result['best_mae']:.6f}, 用时{cell_result['optimization_time']:.1f}s")

                except Exception as e:
                    print(f"❌ 电芯{cell_idx}处理失败: {e}")
                    cluster_result['failed_cells'] += 1

                # 显示簇内进度
                processed_cells = cluster_result['successful_cells'] + cluster_result['failed_cells']
                progress = processed_cells / 400 * 100
                print(f"📊 簇{stack_id}-{cluster_id}进度: {progress:.1f}% ({processed_cells}/400)")

            # 保存簇级汇总
            self._save_cluster_summary(cluster_result, cluster_output_dir)
            cluster_result['status'] = 'success'

        except Exception as e:
            print(f"❌ 簇{stack_id}-{cluster_id}处理失败: {e}")
            cluster_result['status'] = 'failed'
            cluster_result['error'] = str(e)

        cluster_result['processing_time'] = time.time() - cluster_result['start_time']
        return cluster_result

    def _save_cell_detailed_results(self, cell_result, cell_output_dir, cell_idx, stack_id, cluster_id):
        """保存电芯详细结果到独立目录"""

        # 1. 保存电芯详细JSON
        cell_details = {
            'station_id': self.station_id,
            'stack_id': stack_id,
            'cluster_id': cluster_id,
            'cell_id': cell_idx,
            'cell_identifier': f"{self.station_id}_{stack_id}_{cluster_id}_{cell_idx:03d}",
            'optimization_results': {
                'best_mae': cell_result['best_mae'],
                'initial_soc': cell_result['initial_soc'],
                'optimization_time': cell_result['optimization_time'],
                'final_generation': cell_result.get('final_generation', 0),
                'early_stopped': cell_result.get('early_stopped', False),
                'no_improvement_count': cell_result.get('no_improvement_count', 0)
            },
            'ocv_parameters': {
                'soc_points': self.soc_points.tolist(),
                'ocv_points': cell_result['ocv_points'],
                'fixed_r0': self.fixed_r0
            },
            'metadata': {
                'voltage_col': cell_result.get('voltage_col', ''),
                'current_col': cell_result.get('current_col', ''),
                'data_shape': cell_result.get('data_shape', [0, 0]),
                'time_range_start': cell_result.get('time_range_start', ''),
                'time_range_end': cell_result.get('time_range_end', ''),
                'processing_timestamp': cell_result.get('processing_timestamp', ''),
                'gpu_used': cell_result.get('gpu_used', False)
            }
        }

        cell_details_file = os.path.join(cell_output_dir, f"cell_{cell_idx:03d}_details.json")
        with open(cell_details_file, 'w', encoding='utf-8') as f:
            json.dump(cell_details, f, indent=2, ensure_ascii=False)

        # 2. 保存OCV曲线数据CSV
        ocv_data = pd.DataFrame({
            'soc_percent': self.soc_points,
            'ocv_voltage': cell_result['ocv_points'],
            'cell_id': cell_idx,
            'stack_id': stack_id,
            'cluster_id': cluster_id
        })

        ocv_csv_file = os.path.join(cell_output_dir, "ocv_curve_data.csv")
        ocv_data.to_csv(ocv_csv_file, index=False, encoding='utf-8')

        # 3. 重命名图表文件（根据实际生成的文件名格式）
        voltage_col = cell_result.get('voltage_col', '')
        cell_num = voltage_col.split('电压')[-1] if '电压' in voltage_col else str(cell_idx)

        old_comparison = os.path.join(cell_output_dir, f"comparison_{stack_id}_{cluster_id}_{cell_num}.png")
        new_comparison = os.path.join(cell_output_dir, "comparison_plot.png")
        if os.path.exists(old_comparison):
            os.rename(old_comparison, new_comparison)

        old_optimization = os.path.join(cell_output_dir, f"optimization_{stack_id}_{cluster_id}_{cell_num}.png")
        new_optimization = os.path.join(cell_output_dir, "optimization_plot.png")
        if os.path.exists(old_optimization):
            os.rename(old_optimization, new_optimization)

    def _save_cluster_summary(self, cluster_result, cluster_output_dir):
        """保存簇级汇总结果"""

        # 准备簇汇总数据
        cluster_summary_data = []

        for cell_idx, cell_result in cluster_result['cell_results'].items():
            cluster_summary_data.append({
                'station_id': self.station_id,
                'stack_id': cluster_result['stack_id'],
                'cluster_id': cluster_result['cluster_id'],
                'cell_id': cell_idx,
                'cell_identifier': f"{self.station_id}_{cluster_result['stack_id']}_{cluster_result['cluster_id']}_{cell_idx:03d}",
                'best_mae': cell_result['best_mae'],
                'initial_soc': cell_result['initial_soc'],
                'optimization_time': cell_result['optimization_time'],
                'final_generation': cell_result.get('final_generation', 0),
                'early_stopped': cell_result.get('early_stopped', False),
                'no_improvement_count': cell_result.get('no_improvement_count', 0),
                'early_stop_patience': cell_result.get('early_stop_patience', 0),
                'early_stop_threshold': cell_result.get('early_stop_threshold', 0),
                'voltage_col': cell_result.get('voltage_col', ''),
                'current_col': cell_result.get('current_col', ''),
                'processing_timestamp': cell_result.get('processing_timestamp', ''),
                'gpu_used': cell_result.get('gpu_used', False),
                'status': 'success'
            })

        # 保存簇汇总CSV
        if cluster_summary_data:
            cluster_df = pd.DataFrame(cluster_summary_data)
            cluster_csv_file = os.path.join(cluster_output_dir, "cluster_summary.csv")
            cluster_df.to_csv(cluster_csv_file, index=False, encoding='utf-8')

        # 保存簇元数据
        cluster_metadata = {
            'cluster_info': {
                'station_id': self.station_id,
                'stack_id': cluster_result['stack_id'],
                'cluster_id': cluster_result['cluster_id'],
                'file_name': cluster_result['file_name']
            },
            'processing_stats': {
                'total_cells': cluster_result['total_cells'],
                'successful_cells': cluster_result['successful_cells'],
                'failed_cells': cluster_result['failed_cells'],
                'success_rate': cluster_result['successful_cells'] / cluster_result['total_cells'] * 100,
                'processing_time': cluster_result['processing_time'],
                'status': cluster_result['status']
            },
            'performance_stats': {
                'avg_mae': np.mean([r['best_mae'] for r in cluster_result['cell_results'].values()]) if cluster_result['cell_results'] else 0,
                'min_mae': np.min([r['best_mae'] for r in cluster_result['cell_results'].values()]) if cluster_result['cell_results'] else 0,
                'max_mae': np.max([r['best_mae'] for r in cluster_result['cell_results'].values()]) if cluster_result['cell_results'] else 0,
                'avg_optimization_time': np.mean([r['optimization_time'] for r in cluster_result['cell_results'].values()]) if cluster_result['cell_results'] else 0,
                'early_stop_rate': sum([1 for r in cluster_result['cell_results'].values() if r.get('early_stopped', False)]) / len(cluster_result['cell_results']) * 100 if cluster_result['cell_results'] else 0
            }
        }

        cluster_metadata_file = os.path.join(cluster_output_dir, "cluster_metadata.json")
        with open(cluster_metadata_file, 'w', encoding='utf-8') as f:
            json.dump(cluster_metadata, f, indent=2, ensure_ascii=False)

    def _save_stack_summary(self, stack_stats, stack_output_dir):
        """保存堆级汇总结果"""

        # 收集堆内所有电芯数据
        stack_summary_data = []

        for cluster_id, cluster_result in stack_stats['cluster_results'].items():
            if 'cell_results' in cluster_result:
                for cell_idx, cell_result in cluster_result['cell_results'].items():
                    stack_summary_data.append({
                        'station_id': self.station_id,
                        'stack_id': stack_stats['stack_id'],
                        'cluster_id': cluster_id,
                        'cell_id': cell_idx,
                        'cell_identifier': f"{self.station_id}_{stack_stats['stack_id']}_{cluster_id}_{cell_idx:03d}",
                        'best_mae': cell_result['best_mae'],
                        'initial_soc': cell_result['initial_soc'],
                        'optimization_time': cell_result['optimization_time'],
                        'final_generation': cell_result.get('final_generation', 0),
                        'early_stopped': cell_result.get('early_stopped', False),
                        'no_improvement_count': cell_result.get('no_improvement_count', 0),
                        'processing_timestamp': cell_result.get('processing_timestamp', ''),
                        'status': 'success'
                    })

        # 保存堆汇总CSV
        if stack_summary_data:
            stack_df = pd.DataFrame(stack_summary_data)
            stack_csv_file = os.path.join(stack_output_dir, f"stack_{stack_stats['stack_id']}_summary.csv")
            stack_df.to_csv(stack_csv_file, index=False, encoding='utf-8')

    def _save_station_summary(self, station_stats, station_output_dir):
        """保存全站汇总结果"""

        # 收集全站所有电芯数据
        station_summary_data = []

        for stack_id, stack_result in station_stats['stack_results'].items():
            for cluster_id, cluster_result in stack_result['cluster_results'].items():
                if 'cell_results' in cluster_result:
                    for cell_idx, cell_result in cluster_result['cell_results'].items():
                        station_summary_data.append({
                            'station_id': self.station_id,
                            'stack_id': stack_id,
                            'cluster_id': cluster_id,
                            'cell_id': cell_idx,
                            'cell_identifier': f"{self.station_id}_{stack_id}_{cluster_id}_{cell_idx:03d}",
                            'best_mae': cell_result['best_mae'],
                            'initial_soc': cell_result['initial_soc'],
                            'optimization_time': cell_result['optimization_time'],
                            'final_generation': cell_result.get('final_generation', 0),
                            'early_stopped': cell_result.get('early_stopped', False),
                            'no_improvement_count': cell_result.get('no_improvement_count', 0),
                            'early_stop_patience': cell_result.get('early_stop_patience', 0),
                            'early_stop_threshold': cell_result.get('early_stop_threshold', 0),
                            'processing_timestamp': cell_result.get('processing_timestamp', ''),
                            'gpu_used': cell_result.get('gpu_used', False),
                            'status': 'success'
                        })

        # 保存全站汇总CSV
        if station_summary_data:
            station_df = pd.DataFrame(station_summary_data)
            station_csv_file = os.path.join(station_output_dir, "station_summary.csv")
            station_df.to_csv(station_csv_file, index=False, encoding='utf-8')

            # 保存Excel多工作表报告
            excel_file = os.path.join(station_output_dir, "station_summary.xlsx")
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 全站汇总工作表
                station_df.to_excel(writer, sheet_name='全站汇总', index=False)

                # 按堆分组的工作表
                for stack_id in station_stats['stack_results'].keys():
                    stack_data = station_df[station_df['stack_id'] == stack_id]
                    if not stack_data.empty:
                        stack_data.to_excel(writer, sheet_name=f'{stack_id}号堆', index=False)

                # 异常电芯汇总（MAE > 0.02的电芯）
                abnormal_cells = station_df[station_df['best_mae'] > 0.02]
                if not abnormal_cells.empty:
                    abnormal_cells.to_excel(writer, sheet_name='异常电芯', index=False)

                # 统计分析工作表
                stats_data = []
                for stack_id in station_stats['stack_results'].keys():
                    stack_data = station_df[station_df['stack_id'] == stack_id]
                    if not stack_data.empty:
                        stats_data.append({
                            '堆编号': f'{stack_id}号堆',
                            '电芯总数': len(stack_data),
                            '平均MAE': stack_data['best_mae'].mean(),
                            '最小MAE': stack_data['best_mae'].min(),
                            '最大MAE': stack_data['best_mae'].max(),
                            'MAE标准差': stack_data['best_mae'].std(),
                            '平均优化时间(s)': stack_data['optimization_time'].mean(),
                            '早停率(%)': (stack_data['early_stopped'].sum() / len(stack_data)) * 100,
                            '异常电芯数': len(stack_data[stack_data['best_mae'] > 0.02])
                        })

                if stats_data:
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='统计分析', index=False)

        # 保存处理日志
        log_file = os.path.join(station_output_dir, "processing_log.txt")
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"127电站{station_stats['date']}数据处理日志\n")
            f.write(f"{'='*60}\n\n")
            f.write(f"处理时间: {datetime.now().isoformat()}\n")
            f.write(f"总用时: {station_stats['total_time']:.1f}秒 ({station_stats['total_time']/3600:.2f}小时)\n\n")

            f.write(f"文件处理统计:\n")
            f.write(f"  总文件数: {station_stats['total_files']}\n")
            f.write(f"  成功文件: {station_stats['successful_files']}\n")
            f.write(f"  失败文件: {station_stats['failed_files']}\n")
            f.write(f"  成功率: {station_stats['successful_files']/station_stats['total_files']*100:.1f}%\n\n")

            f.write(f"电芯处理统计:\n")
            f.write(f"  总电芯数: {station_stats['total_cells']}\n")
            f.write(f"  成功电芯: {station_stats['successful_cells']}\n")
            f.write(f"  失败电芯: {station_stats['failed_cells']}\n")
            f.write(f"  成功率: {station_stats['successful_cells']/station_stats['total_cells']*100:.1f}%\n\n")

            f.write(f"详细处理记录:\n")
            f.write(f"{'='*60}\n")

            for file_result in station_stats['file_results']:
                f.write(f"\n文件: {file_result['file_name']}\n")
                f.write(f"  堆{file_result['stack_id']}簇{file_result['cluster_id']}\n")
                f.write(f"  状态: {file_result['status']}\n")
                f.write(f"  用时: {file_result['processing_time']:.1f}s\n")
                f.write(f"  成功电芯: {file_result['successful_cells']}/400\n")
                if file_result['status'] == 'failed' and 'error' in file_result:
                    f.write(f"  错误: {file_result['error']}\n")


# 使用示例
if __name__ == "__main__":
    # 配置参数
    data_dir = "../database"
    output_base_dir = "./127_station_results"

    # 优化参数（平衡速度和精度的配置 + 早停功能）
    optimization_params = {
        'use_actual_soc': True,
        'soc_tolerance': 0,
        'population_size': 100,  # 减少种群大小加快速度
        'n_generations': 150,    # 减少代数加快速度
        'crossover_prob': 0.7,
        'mutation_prob': 0.15,
        'save_interval': 5,      # 更频繁保存
        # 早停参数
        'early_stop_patience': 30,    # 连续30代无改善就停止（更激进）
        'early_stop_threshold': 1e-6  # 改善小于1e-6认为无改善
    }

    # 创建优化器
    optimizer = Battery400CellsOriginalCore(data_dir, output_base_dir)

    # 处理127电站2023年12月25日的所有簇数据
    print(f"\n🚀 开始处理127电站2023年12月25日的完整数据")
    print(f"📊 预计处理: 2个堆 × 7个簇 × 400个电芯 = 5600个电芯")
    print(f"⏱️  预计用时: 约2.8小时")

    station_result = optimizer.process_station_all_clusters(
        date="2023-12-25",
        optimization_params=optimization_params
    )

    print(f"\n{'='*80}")
    print(f"🎉 127电站ECM参数辨识完成!")
    print(f"{'='*80}")
    print(f"📊 处理统计:")
    print(f"  📁 文件: 成功{station_result['successful_files']}/{station_result['total_files']}")
    print(f"  🔋 电芯: 成功{station_result['successful_cells']}, 失败{station_result['failed_cells']}")
    print(f"  ⏱️  总用时: {station_result['total_time']:.1f}s ({station_result['total_time']/3600:.2f}小时)")
    print(f"  📁 结果目录: {output_base_dir}")
    print(f"\n📋 输出结构:")
    print(f"  ├── station_summary.csv          # 全站5600个电芯汇总")
    print(f"  ├── station_summary.xlsx         # Excel多工作表报告")
    print(f"  ├── processing_log.txt           # 详细处理日志")
    print(f"  ├── stack_1/                     # 1号堆结果")
    print(f"  │   ├── cluster_1/               # 1号簇结果")
    print(f"  │   │   ├── cell_001/            # 电芯1详细结果")
    print(f"  │   │   │   ├── comparison_plot.png")
    print(f"  │   │   │   ├── optimization_plot.png")
    print(f"  │   │   │   ├── cell_001_details.json")
    print(f"  │   │   │   └── ocv_curve_data.csv")
    print(f"  │   │   ├── cell_002/ ... cell_400/")
    print(f"  │   │   ├── cluster_summary.csv  # 簇级汇总")
    print(f"  │   │   └── cluster_metadata.json")
    print(f"  │   ├── cluster_2/ ... cluster_7/")
    print(f"  │   └── stack_1_summary.csv      # 堆级汇总")
    print(f"  └── stack_2/                     # 2号堆结果")
    print(f"{'='*80}")
