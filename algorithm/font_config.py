#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体配置模块
统一管理matplotlib中文字体显示设置
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os

def setup_chinese_font(verbose=False):
    """
    设置matplotlib中文字体显示
    
    Args:
        verbose (bool): 是否显示详细信息
        
    Returns:
        bool: 是否成功加载自定义字体文件
    """
    font_loaded = False
    
    try:
        # 尝试加载当前目录的SimHei字体文件
        font_path = 'simhei.ttf'
        if os.path.exists(font_path):
            # 添加字体到matplotlib字体管理器
            fm.fontManager.addfont(font_path)
            
            # 获取字体属性
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            
            # 设置为默认字体
            plt.rcParams['font.sans-serif'] = [font_name, 'SimHei']
            font_loaded = True
            
            if verbose:
                print(f"✅ 成功加载自定义字体: {font_name}")
                
        else:
            if verbose:
                print(f"⚠️  字体文件不存在: {font_path}")
                
    except Exception as e:
        if verbose:
            print(f"❌ 字体文件加载失败: {e}")
    
    # 设置备用字体列表（按优先级排序）
    fallback_fonts = [
        'SimHei',           # Windows系统字体
        'Microsoft YaHei',  # Windows系统字体
        'PingFang SC',      # macOS系统字体
        'Hiragino Sans GB', # macOS系统字体
        'WenQuanYi Micro Hei', # Linux系统字体
        'DejaVu Sans',      # 通用字体
        'Arial Unicode MS'  # 通用Unicode字体
    ]
    
    # 如果没有加载自定义字体，使用系统字体
    if not font_loaded:
        plt.rcParams['font.sans-serif'] = fallback_fonts
        if verbose:
            print("🔄 使用系统默认中文字体")
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置字体大小
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 12
    plt.rcParams['axes.labelsize'] = 10
    plt.rcParams['xtick.labelsize'] = 9
    plt.rcParams['ytick.labelsize'] = 9
    plt.rcParams['legend.fontsize'] = 9
    
    if verbose:
        print(f"📝 当前字体设置: {plt.rcParams['font.sans-serif']}")
        
    return font_loaded

if __name__ == "__main__":
    print("="*50)
    print("中文字体配置测试")
    print("="*50)
    
    # 测试字体设置
    setup_chinese_font(verbose=True)
    print("\n✅ 字体配置完成")
