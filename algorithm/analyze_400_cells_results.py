#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
400电芯ECM参数辨识结果分析工具

主要功能:
- 加载和分析400电芯辨识结果
- 生成统计报告和可视化图表
- 电芯性能对比分析
- 异常电芯检测和报告

作者: ECM团队
日期: 2025-08-08
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 配置和字体设置
from config import Config
from font_config import setup_chinese_font

# 初始化字体
setup_chinese_font(verbose=False)

class CellResultsAnalyzer:
    """400电芯结果分析器"""
    
    def __init__(self, results_dir):
        self.results_dir = results_dir
        self.results_data = None
        self.summary_stats = None
        
    def load_results(self, json_file=None):
        """加载400电芯辨识结果"""
        if json_file is None:
            # 自动查找最新的结果文件
            json_files = glob.glob(os.path.join(self.results_dir, "*complete_results*.json"))
            if not json_files:
                raise FileNotFoundError(f"在 {self.results_dir} 中未找到结果文件")
            json_file = max(json_files, key=os.path.getmtime)
        
        print(f"加载结果文件: {json_file}")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.results_data = data['results']
        self.metadata = data.get('metadata', {})
        self.statistics = data.get('statistics', {})
        
        print(f"成功加载 {len(self.results_data)} 个电芯的结果")
        return self.results_data
    
    def generate_summary_stats(self):
        """生成汇总统计信息"""
        if self.results_data is None:
            raise ValueError("请先加载结果数据")
        
        successful_cells = []
        failed_cells = []
        
        for cell_id, result in self.results_data.items():
            if 'best_mae' in result:
                successful_cells.append({
                    'cell_index': result['cell_index'],
                    'best_mae': result['best_mae'],
                    'initial_soc': result['initial_soc'],
                    'optimization_time': result['optimization_time'],
                    'final_generation': result['final_generation']
                })
            else:
                failed_cells.append({
                    'cell_index': result['cell_index'],
                    'error': result.get('error', 'Unknown')
                })
        
        self.summary_stats = {
            'total_cells': len(self.results_data),
            'successful_cells': len(successful_cells),
            'failed_cells': len(failed_cells),
            'success_rate': len(successful_cells) / len(self.results_data) * 100,
            'successful_data': pd.DataFrame(successful_cells),
            'failed_data': pd.DataFrame(failed_cells)
        }
        
        return self.summary_stats
    
    def plot_mae_distribution(self, save_path=None):
        """绘制MAE分布图"""
        if self.summary_stats is None:
            self.generate_summary_stats()
        
        df = self.summary_stats['successful_data']
        if df.empty:
            print("没有成功的结果可以绘制")
            return
        
        plt.figure(figsize=(12, 8))
        
        # MAE分布直方图
        plt.subplot(2, 2, 1)
        plt.hist(df['best_mae'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('MAE (V)')
        plt.ylabel('电芯数量')
        plt.title('电芯MAE分布')
        plt.grid(True, alpha=0.3)
        
        # MAE箱线图
        plt.subplot(2, 2, 2)
        plt.boxplot(df['best_mae'])
        plt.ylabel('MAE (V)')
        plt.title('电芯MAE箱线图')
        plt.grid(True, alpha=0.3)
        
        # 电芯索引 vs MAE
        plt.subplot(2, 2, 3)
        plt.scatter(df['cell_index'], df['best_mae'], alpha=0.6, s=20)
        plt.xlabel('电芯编号')
        plt.ylabel('MAE (V)')
        plt.title('电芯编号 vs MAE')
        plt.grid(True, alpha=0.3)
        
        # 优化时间分布
        plt.subplot(2, 2, 4)
        plt.hist(df['optimization_time'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        plt.xlabel('优化时间 (s)')
        plt.ylabel('电芯数量')
        plt.title('优化时间分布')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"MAE分布图已保存: {save_path}")
        else:
            plt.show()
    
    def plot_soc_distribution(self, save_path=None):
        """绘制初始SOC分布图"""
        if self.summary_stats is None:
            self.generate_summary_stats()
        
        df = self.summary_stats['successful_data']
        if df.empty:
            print("没有成功的结果可以绘制")
            return
        
        plt.figure(figsize=(10, 6))
        
        plt.subplot(1, 2, 1)
        plt.hist(df['initial_soc'], bins=20, alpha=0.7, color='orange', edgecolor='black')
        plt.xlabel('初始SOC (%)')
        plt.ylabel('电芯数量')
        plt.title('电芯初始SOC分布')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        plt.scatter(df['cell_index'], df['initial_soc'], alpha=0.6, s=20, color='orange')
        plt.xlabel('电芯编号')
        plt.ylabel('初始SOC (%)')
        plt.title('电芯编号 vs 初始SOC')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"SOC分布图已保存: {save_path}")
        else:
            plt.show()
    
    def identify_outliers(self, mae_threshold=None, soc_threshold=None):
        """识别异常电芯"""
        if self.summary_stats is None:
            self.generate_summary_stats()
        
        df = self.summary_stats['successful_data']
        if df.empty:
            print("没有成功的结果可以分析")
            return {}
        
        outliers = {}
        
        # MAE异常检测
        if mae_threshold is None:
            mae_threshold = df['best_mae'].mean() + 2 * df['best_mae'].std()
        
        mae_outliers = df[df['best_mae'] > mae_threshold]
        outliers['high_mae'] = {
            'threshold': mae_threshold,
            'count': len(mae_outliers),
            'cells': mae_outliers['cell_index'].tolist(),
            'details': mae_outliers.to_dict('records')
        }
        
        # SOC异常检测
        if soc_threshold is None:
            soc_mean = df['initial_soc'].mean()
            soc_std = df['initial_soc'].std()
            soc_lower = soc_mean - 2 * soc_std
            soc_upper = soc_mean + 2 * soc_std
        else:
            soc_lower, soc_upper = soc_threshold
        
        soc_outliers = df[(df['initial_soc'] < soc_lower) | (df['initial_soc'] > soc_upper)]
        outliers['abnormal_soc'] = {
            'threshold': (soc_lower, soc_upper),
            'count': len(soc_outliers),
            'cells': soc_outliers['cell_index'].tolist(),
            'details': soc_outliers.to_dict('records')
        }
        
        return outliers
    
    def generate_report(self, output_file=None):
        """生成分析报告"""
        if self.summary_stats is None:
            self.generate_summary_stats()
        
        df = self.summary_stats['successful_data']
        outliers = self.identify_outliers()
        
        report = []
        report.append("=" * 80)
        report.append("400电芯ECM参数辨识结果分析报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"结果目录: {self.results_dir}")
        report.append("")
        
        # 基本统计
        report.append("1. 基本统计信息")
        report.append("-" * 40)
        report.append(f"总电芯数: {self.summary_stats['total_cells']}")
        report.append(f"成功处理: {self.summary_stats['successful_cells']}")
        report.append(f"处理失败: {self.summary_stats['failed_cells']}")
        report.append(f"成功率: {self.summary_stats['success_rate']:.2f}%")
        report.append("")
        
        if not df.empty:
            # MAE统计
            report.append("2. MAE统计信息")
            report.append("-" * 40)
            report.append(f"平均MAE: {df['best_mae'].mean():.6f} V")
            report.append(f"MAE标准差: {df['best_mae'].std():.6f} V")
            report.append(f"最小MAE: {df['best_mae'].min():.6f} V (电芯 {df.loc[df['best_mae'].idxmin(), 'cell_index']})")
            report.append(f"最大MAE: {df['best_mae'].max():.6f} V (电芯 {df.loc[df['best_mae'].idxmax(), 'cell_index']})")
            report.append("")
            
            # SOC统计
            report.append("3. 初始SOC统计信息")
            report.append("-" * 40)
            report.append(f"平均初始SOC: {df['initial_soc'].mean():.2f}%")
            report.append(f"SOC标准差: {df['initial_soc'].std():.2f}%")
            report.append(f"SOC范围: {df['initial_soc'].min():.2f}% - {df['initial_soc'].max():.2f}%")
            report.append("")
            
            # 优化时间统计
            report.append("4. 优化时间统计")
            report.append("-" * 40)
            report.append(f"平均优化时间: {df['optimization_time'].mean():.1f} s")
            report.append(f"总优化时间: {df['optimization_time'].sum():.1f} s")
            report.append(f"时间范围: {df['optimization_time'].min():.1f}s - {df['optimization_time'].max():.1f}s")
            report.append("")
            
            # 异常检测
            report.append("5. 异常电芯检测")
            report.append("-" * 40)
            report.append(f"高MAE异常电芯: {outliers['high_mae']['count']} 个")
            if outliers['high_mae']['count'] > 0:
                report.append(f"  阈值: {outliers['high_mae']['threshold']:.6f} V")
                report.append(f"  电芯编号: {outliers['high_mae']['cells']}")
            
            report.append(f"SOC异常电芯: {outliers['abnormal_soc']['count']} 个")
            if outliers['abnormal_soc']['count'] > 0:
                lower, upper = outliers['abnormal_soc']['threshold']
                report.append(f"  正常范围: {lower:.2f}% - {upper:.2f}%")
                report.append(f"  电芯编号: {outliers['abnormal_soc']['cells']}")
        
        report.append("")
        report.append("=" * 80)
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"分析报告已保存: {output_file}")
        else:
            print(report_text)
        
        return report_text


# 使用示例
if __name__ == "__main__":
    # 指定结果目录
    results_dir = "./400_cells_results/2023-12-25_400_cells"
    
    if os.path.exists(results_dir):
        # 创建分析器
        analyzer = CellResultsAnalyzer(results_dir)
        
        try:
            # 加载结果
            analyzer.load_results()
            
            # 生成统计信息
            stats = analyzer.generate_summary_stats()
            
            # 生成可视化图表
            analyzer.plot_mae_distribution(os.path.join(results_dir, "mae_distribution.png"))
            analyzer.plot_soc_distribution(os.path.join(results_dir, "soc_distribution.png"))
            
            # 生成分析报告
            analyzer.generate_report(os.path.join(results_dir, "analysis_report.txt"))
            
            print("\n分析完成！")
            print(f"图表和报告已保存在: {results_dir}")
            
        except Exception as e:
            print(f"分析过程中发生错误: {e}")
    else:
        print(f"结果目录不存在: {results_dir}")
        print("请先运行400电芯辨识程序生成结果")
