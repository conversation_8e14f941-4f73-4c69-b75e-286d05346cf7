# 400电芯ECM参数辨识系统

基于遗传算法的400电芯等效电路模型(ECM)参数循环辨识系统，支持GPU/CPU混合加速计算。

## 📋 系统概述

### 主要功能
- **400电芯循环辨识**: 自动对每个电芯进行独立的ECM参数优化
- **GPU/CPU加速**: 支持CUDA加速和CPU并行计算
- **智能数据处理**: 自动检测电流、电压、SOC列
- **批量文件处理**: 支持多个HDF5文件的批量处理
- **结果分析可视化**: 自动生成统计报告和可视化图表
- **异常检测**: 识别性能异常的电芯

### 核心文件
- `ecm_ga_400_cells_optimizer.py` - 400电芯优化器核心代码
- `analyze_400_cells_results.py` - 结果分析和可视化工具
- `run_400_cells_batch.py` - 批处理脚本
- `README_400_cells.md` - 使用说明文档

## 🚀 快速开始

### 1. 环境要求
```bash
# 基础依赖
numpy >= 1.20.0
pandas >= 1.3.0
matplotlib >= 3.3.0
scipy >= 1.7.0
deap >= 1.3.0

# 可选GPU加速
cupy >= 9.0.0  # CUDA支持

# 数据处理
h5py >= 3.0.0
seaborn >= 0.11.0
```

### 2. 数据准备
确保您的HDF5数据文件包含以下列：
- **电流列**: 包含"电流"关键字，如 `2023-12-25_1#BMS-堆内-1簇组电流`
- **电压列**: 包含"单体电压"关键字，如 `2023-12-25_1#BMS-堆内-1簇单体电压1` 到 `单体电压400`
- **SOC列**: 包含"SOC"关键字（可选）

### 3. 快速运行

#### 方法一：使用批处理脚本（推荐）
```bash
# 1. 修改配置参数
vim run_400_cells_batch.py

# 2. 运行批处理
python run_400_cells_batch.py
```

#### 方法二：单文件处理
```python
from ecm_ga_400_cells_optimizer import process_single_file_400_cells

# 处理单个文件
result = process_single_file_400_cells(
    file_path="./database/127_1_1_2023-12-25.h5",
    output_base_dir="./400_cells_results",
    start_cell=1,      # 起始电芯
    end_cell=10,       # 结束电芯（测试用）
    optimization_params={
        'population_size': 200,
        'n_generations': 300,
        'use_actual_soc': True
    }
)
```

## ⚙️ 配置参数

### 核心参数配置
```python
# 电芯范围
start_cell = 1        # 起始电芯编号
end_cell = 400        # 结束电芯编号

# 遗传算法参数
optimization_params = {
    'use_actual_soc': True,      # 使用实际SOC数据
    'soc_tolerance': 0,          # SOC容差范围(%)
    'population_size': 200,      # 种群大小
    'n_generations': 300,        # 进化代数
    'crossover_prob': 0.7,       # 交叉概率
    'mutation_prob': 0.15,       # 变异概率
    'save_interval': 10,         # 保存间隔
}
```

### 性能调优建议
```python
# 快速测试配置
quick_params = {
    'population_size': 100,      # 减少种群大小
    'n_generations': 200,        # 减少代数
    'start_cell': 1,
    'end_cell': 10               # 只处理前10个电芯
}

# 高精度配置
precision_params = {
    'population_size': 300,      # 增加种群大小
    'n_generations': 500,        # 增加代数
    'start_cell': 1,
    'end_cell': 400              # 处理所有电芯
}
```

## 📊 结果分析

### 自动生成的结果文件
```
400_cells_results/
├── 2023-12-25_400_cells/
│   ├── 400_cells_complete_results_20250808_143022.json  # 完整结果
│   ├── 400_cells_summary_20250808_143022.csv           # 汇总表格
│   ├── mae_distribution.png                            # MAE分布图
│   ├── soc_distribution.png                            # SOC分布图
│   └── analysis_report.txt                             # 分析报告
```

### 结果分析工具
```python
from analyze_400_cells_results import CellResultsAnalyzer

# 创建分析器
analyzer = CellResultsAnalyzer("./400_cells_results/2023-12-25_400_cells")

# 加载结果
analyzer.load_results()

# 生成统计信息
stats = analyzer.generate_summary_stats()

# 生成可视化图表
analyzer.plot_mae_distribution("mae_dist.png")
analyzer.plot_soc_distribution("soc_dist.png")

# 生成分析报告
analyzer.generate_report("report.txt")

# 异常检测
outliers = analyzer.identify_outliers()
```

## 📈 性能指标

### 典型处理性能
- **单电芯优化时间**: 30-60秒（取决于硬件配置）
- **400电芯总用时**: 3-6小时（GPU加速）
- **内存使用**: 2-8GB（取决于数据大小）
- **GPU内存**: 1-4GB（CUDA模式）

### 优化建议
1. **GPU加速**: 安装CuPy启用CUDA加速，可提升2-3倍速度
2. **参数调优**: 减少种群大小和代数可显著加快速度
3. **分批处理**: 可以分批处理电芯，避免长时间运行
4. **内存管理**: 大数据集建议增加系统内存

## 🔧 故障排除

### 常见问题

#### 1. CUDA相关错误
```bash
# 检查CUDA安装
nvidia-smi
python -c "import cupy; print(cupy.cuda.runtime.getDeviceCount())"

# 如果CUDA不可用，系统会自动回退到CPU模式
```

#### 2. 内存不足
```python
# 减少批处理大小
optimization_params['population_size'] = 100  # 减少种群大小

# 或分批处理电芯
start_cell = 1
end_cell = 50  # 先处理前50个电芯
```

#### 3. 列名检测失败
```python
# 检查数据文件结构
import pandas as pd
data = pd.read_hdf("your_file.h5")
print("可用列名:")
print([col for col in data.columns if '电流' in col or '电压' in col])
```

#### 4. 优化收敛慢
```python
# 调整遗传算法参数
optimization_params.update({
    'population_size': 300,      # 增加种群大小
    'n_generations': 500,        # 增加代数
    'mutation_prob': 0.2         # 增加变异概率
})
```

## 📝 输出结果说明

### JSON结果文件结构
```json
{
  "results": {
    "1": {
      "cell_index": 1,
      "voltage_col": "2023-12-25_1#BMS-堆内-1簇单体电压1",
      "best_mae": 0.013456,
      "initial_soc": 28.5,
      "ocv_points": [2.95, 2.96, ...],
      "optimization_time": 45.2,
      "fitness_history": [0.1, 0.08, ...]
    }
  },
  "statistics": {
    "successful_cells": 395,
    "failed_cells": 5,
    "total_time": 18000
  }
}
```

### CSV汇总文件字段
- `cell_index`: 电芯编号
- `voltage_col`: 电压列名
- `best_mae`: 最佳平均绝对误差(V)
- `initial_soc`: 优化得到的初始SOC(%)
- `optimization_time`: 优化用时(s)
- `status`: 处理状态(success/failed)

## 🤝 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

---

**注意**: 首次运行建议使用小范围电芯（如1-10）进行测试，确认系统正常工作后再进行全量处理。
