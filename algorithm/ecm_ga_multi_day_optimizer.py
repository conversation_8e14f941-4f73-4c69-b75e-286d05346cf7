#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于遗传算法(GA)的ECM电池参数识别器 - 400电芯循环辨识版

主要功能:
- 针对400个电芯的OCV-SOC曲线参数识别
- 多电芯循环处理与并行计算
- GPU/CPU混合加速计算
- 智能错误处理和进度监控
- 完整的可视化和验证功能
- 电芯级别的结果保存与分析

作者: 基于ecm_ga_multi_day_optimizer.py扩展
日期: 2025-08-08
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import random
import time
import warnings
import os
import glob
import json
from datetime import datetime
warnings.filterwarnings('ignore')

# 遗传算法相关导入
from deap import base, creator, tools

# CUDA并行计算相关导入
try:
    import cupy as cp
    CUDA_AVAILABLE = True
    print("CUDA支持已启用 (CuPy)")
except ImportError:
    import numpy as cp  # 回退到numpy
    CUDA_AVAILABLE = False
    print("CUDA不可用，使用CPU计算")

# 多进程并行计算
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import threading
from functools import partial

# 配置和字体设置
from config import Config
from font_config import setup_chinese_font

# 初始化配置和字体
setup_chinese_font(verbose=True)


class BatteryParameterOptimizerGA:
    """基于遗传算法的ECM电池参数优化器（支持CUDA加速）"""

    def __init__(self, data, current_col, voltage_col, use_cuda=True, n_workers=None):
        self.data = data
        self.current_col = current_col
        self.voltage_col = voltage_col

        # CUDA和并行设置
        self.use_cuda = use_cuda and CUDA_AVAILABLE
        self.n_workers = n_workers or Config.get_max_cpu_workers()

        # GPU优化参数
        self.gpu_batch_size = Config.GPU_BATCH_SIZE if self.use_cuda else Config.CPU_BATCH_SIZE
        self.memory_pool_limit = Config.get_gpu_memory_limit_bytes() if self.use_cuda else None

        # 从配置文件加载参数
        self.soc_points = Config.SOC_POINTS
        self.fixed_r0 = Config.FIXED_R0
        self.default_ocv = Config.DEFAULT_OCV
        self.ocv_bounds = Config.get_ocv_bounds()
        
        # 优化过程记录
        self.optimization_history = {
            'generation': [],
            'best_fitness': [],
            'best_ocv': []
        }
        
        # GA算法相关变量
        self.lb = None  # 下边界
        self.ub = None  # 上边界
        self.toolbox = None

        # 初始化CUDA数据（在所有属性定义完成后）
        if self.use_cuda:
            print(f"启用CUDA加速计算")
            # 将数据转移到GPU
            self._setup_cuda_data()
        else:
            print(f"使用CPU并行计算 ({self.n_workers}个进程)")

        print(f"计算设备: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")

    def _setup_cuda_data(self):
        """设置CUDA数据"""
        if not self.use_cuda:
            return

        try:
            # 设置GPU内存池限制
            if self.memory_pool_limit:
                mempool = cp.get_default_memory_pool()
                mempool.set_limit(size=self.memory_pool_limit)

            # 将关键数据转移到GPU，使用优化的数据类型
            self.data_gpu = {}
            self.data_gpu['current'] = cp.array(self.data[self.current_col].values, dtype=cp.float32)
            self.data_gpu['voltage'] = cp.array(self.data[self.voltage_col].values, dtype=cp.float32)
            self.data_gpu['time_diff'] = cp.array(self._calculate_time_diff(), dtype=cp.float32)
            self.data_gpu['soc_points'] = cp.array(self.soc_points, dtype=cp.float32)
            self.data_gpu['default_ocv'] = cp.array(self.default_ocv, dtype=cp.float32)
            self.data_gpu['ocv_bounds_lower'] = cp.array(self.ocv_bounds[0], dtype=cp.float32)
            self.data_gpu['ocv_bounds_upper'] = cp.array(self.ocv_bounds[1], dtype=cp.float32)

            # 预计算常用数据
            self.data_gpu['capacity_Ah'] = cp.float32(Config.BATTERY_CAPACITY)
            self.data_gpu['fixed_r0'] = cp.float32(self.fixed_r0)

            # 获取GPU内存使用情况
            mempool = cp.get_default_memory_pool()
            used_mb = mempool.used_bytes() / 1024 / 1024
            print(f"数据已转移到GPU内存 (使用 {used_mb:.1f}MB)")
        except Exception as e:
            print(f"GPU数据设置失败，回退到CPU: {e}")
            self.use_cuda = False

    def _calculate_time_diff(self):
        """计算时间差（小时）"""
        delta_t = np.zeros(len(self.data))
        delta_t[1:] = np.diff(self.data.index.values) / np.timedelta64(1, 'h')
        return delta_t
    
    def simulate_battery(self, params, plot_comparison=False, plot_filename=None):
        """使用给定参数运行电池模拟（支持CUDA加速）"""
        if self.use_cuda:
            return self._simulate_battery_cuda(params, plot_comparison, plot_filename)
        else:
            return self._simulate_battery_cpu(params, plot_comparison, plot_filename)

    def _simulate_battery_cuda(self, params, plot_comparison=False, plot_filename=None):
        """CUDA加速的电池模拟"""
        initial_soc = params[0]
        ocv_points = cp.array(params[1:])  # 转换为GPU数组
        capacity_Ah = Config.BATTERY_CAPACITY

        try:
            # GPU上的线性插值（简化版本）
            soc_gpu = self.data_gpu['soc_points']

            # 计算SOC
            current_gpu = self.data_gpu['current']
            delta_t_gpu = self.data_gpu['time_diff']
            delta_soc = current_gpu * delta_t_gpu / capacity_Ah * 100
            soc = initial_soc - cp.cumsum(delta_soc)
            soc = cp.clip(soc, 0, 100)

            # GPU上的线性插值计算OCV
            ocv = self._cuda_linear_interp(soc, soc_gpu, ocv_points)

            # 计算端电压
            voltage = ocv - current_gpu * self.fixed_r0

            # 计算MAE
            mae = cp.mean(cp.abs(voltage - self.data_gpu['voltage']))

            # 转回CPU用于绘图
            if plot_comparison:
                voltage_cpu = cp.asnumpy(voltage)
                soc_cpu = cp.asnumpy(soc)
                # 创建插值函数用于绘图
                ocv_interp = interp1d(self.soc_points, cp.asnumpy(ocv_points),
                                     kind='linear', fill_value="extrapolate")
                self._plot_comparison(voltage_cpu, soc_cpu, initial_soc, capacity_Ah, ocv_interp, plot_filename)

            return float(cp.asnumpy(mae))

        except Exception as e:
            print(f"CUDA模拟失败，回退到CPU: {e}")
            return self._simulate_battery_cpu(params, plot_comparison, plot_filename)

    def _simulate_battery_cpu(self, params, plot_comparison=False, plot_filename=None):
        """CPU版本的电池模拟"""
        initial_soc = params[0]
        ocv_points = params[1:]  # 所有剩余参数都是OCV点
        capacity_Ah = Config.BATTERY_CAPACITY

        # 验证参数长度
        assert len(ocv_points) == len(self.soc_points), "OCV点数量必须与SOC点相同"

        try:
            # 创建OCV插值函数
            ocv_interp = interp1d(self.soc_points, ocv_points, kind='linear',
                                 fill_value="extrapolate")
        except ValueError as e:
            raise ValueError(f"插值函数创建失败: {str(e)}")

        # 计算时间差（小时）
        delta_t = self._calculate_time_diff()

        # 计算SOC
        current_values = self.data[self.current_col].values
        delta_soc = current_values * delta_t / capacity_Ah * 100
        soc = initial_soc - np.cumsum(delta_soc)
        soc = np.clip(soc, 0, 100)

        # 计算端电压 (使用固定R0)
        ocv = ocv_interp(soc)
        voltage = ocv - current_values * self.fixed_r0

        # 计算MAE
        mae = np.mean(np.abs(voltage - self.data[self.voltage_col]))

        # 绘图
        if plot_comparison:
            self._plot_comparison(voltage, soc, initial_soc, capacity_Ah, ocv_interp, plot_filename)

        return mae

    def _cuda_linear_interp(self, x, xp, fp):
        """CUDA线性插值函数"""
        # 简化的线性插值实现
        indices = cp.searchsorted(xp, x, side='right') - 1
        indices = cp.clip(indices, 0, len(xp) - 2)

        x0 = xp[indices]
        x1 = xp[indices + 1]
        y0 = fp[indices]
        y1 = fp[indices + 1]

        # 线性插值公式
        t = (x - x0) / (x1 - x0)
        return y0 + t * (y1 - y0)
    
    def _plot_comparison(self, sim_voltage, sim_soc, initial_soc, capacity_Ah, ocv_interp, plot_filename=None):
        """绘制对比图"""
        plt.figure(figsize=Config.COMPARISON_PLOT_SIZE)
        
        # 电压对比
        plt.subplot(3, 1, 1)
        plt.plot(self.data.index, self.data[self.voltage_col], 'b-', label='真实电压')
        plt.plot(self.data.index, sim_voltage, 'r--', label='模拟电压')
        plt.ylabel('电压(V)')
        plt.title(f'电池模拟 (容量={capacity_Ah}Ah, 初始SOC={initial_soc}%, 固定R0={self.fixed_r0}Ω)')
        plt.legend()
        plt.grid(True)
        
        # SOC变化
        plt.subplot(3, 1, 2)
        plt.plot(self.data.index, sim_soc, 'g-', label='模拟SOC')
        # 动态检查SOC列名
        soc_col_candidates = [col for col in self.data.columns if 'SOC' in col]
        if soc_col_candidates:
            bms_soc_col = soc_col_candidates[0] # 使用第一个找到的SOC列
            plt.plot(self.data.index, self.data[bms_soc_col], 'r-', label='BMS_SOC')
        plt.ylabel('SOC(%)')
        plt.legend()
        plt.grid(True)
        
        # OCV变化
        plt.subplot(3, 1, 3)
        plt.plot(self.data.index, ocv_interp(sim_soc), 'm-', label='OCV')
        plt.xlabel('时间')
        plt.ylabel('OCV(V)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        
        if plot_filename:
            plt.savefig(plot_filename, dpi=Config.PLOT_DPI, bbox_inches='tight')
            plt.close() # 关闭图形，避免内存泄漏
        else:
            plt.show()

    def objective_function(self, params):
        """目标函数，只优化OCV曲线（与原版本完全相同）"""
        # 提取参数 (初始SOC + OCV点)
        ocv_points = params[1:]

        # 1. OCV约束检查
        ocv_penalty = 0

        # 检查OCV是否在允许范围内
        if not np.all((ocv_points >= self.ocv_bounds[0]) & (ocv_points <= self.ocv_bounds[1])):
            # 计算超出边界的惩罚
            lower_violation = np.sum(np.maximum(self.ocv_bounds[0] - ocv_points, 0))
            upper_violation = np.sum(np.maximum(ocv_points - self.ocv_bounds[1], 0))
            ocv_penalty += Config.OCV_BOUNDARY_PENALTY * (lower_violation + upper_violation)

        # 检查OCV单调性
        ocv_diff = np.diff(ocv_points)
        if not np.all(ocv_diff >= 0):
            ocv_penalty += Config.OCV_MONOTONIC_PENALTY * np.sum(np.abs(ocv_diff[ocv_diff < 0]))

        # 2. 运行模拟计算MAE
        try:
            mae = self.simulate_battery(params)
        except Exception as e:
            print(f"模拟失败: {str(e)}")
            return (1e6,)  # DEAP需要返回元组

        # 总目标值 = MAE + OCV惩罚项
        fitness = mae + ocv_penalty
        return (fitness,)  # DEAP需要返回元组

    def _setup_ga(self, population_size=100):
        """设置遗传算法"""
        # 创建适应度类和个体类
        # 为了避免重复创建，先检查
        if not hasattr(creator, "FitnessMin"):
            creator.create("FitnessMin", base.Fitness, weights=(-1.0,))  # 最小化问题
        if not hasattr(creator, "Individual"):
            creator.create("Individual", list, fitness=creator.FitnessMin)

        # 创建工具箱
        self.toolbox = base.Toolbox()

        # 参数维度
        n_params = len(self.lb)

        # 注册基因生成函数
        for i in range(n_params):
            self.toolbox.register(f"attr_{i}", random.uniform, self.lb[i], self.ub[i])

        # 注册个体和种群生成函数
        self.toolbox.register("individual", tools.initCycle, creator.Individual,
                             [getattr(self.toolbox, f"attr_{i}") for i in range(n_params)], n=1)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)

        # 注册遗传算子
        self.toolbox.register("evaluate", self.objective_function)
        self.toolbox.register("mate", tools.cxBlend, alpha=Config.BLEND_ALPHA)  # 混合交叉
        self.toolbox.register("mutate", self._bounded_mutation, indpb=Config.MUTATION_PROB)  # 有界变异
        self.toolbox.register("select", tools.selTournament, tournsize=Config.TOURNAMENT_SIZE)  # 锦标赛选择

    def _bounded_mutation(self, individual, indpb):
        """有界变异操作"""
        for i in range(len(individual)):
            if random.random() < indpb:
                # 高斯变异
                sigma = (self.ub[i] - self.lb[i]) * Config.MUTATION_STRENGTH
                individual[i] += random.gauss(0, sigma)
                # 边界约束
                individual[i] = max(self.lb[i], min(self.ub[i], individual[i]))
        return (individual,)

    def _parallel_evaluate(self, population):
        """并行评估种群适应度"""
        if self.use_cuda:
            # CUDA模式：批量评估
            return self._cuda_batch_evaluate(population)
        else:
            # CPU模式：多进程并行评估
            return self._cpu_parallel_evaluate(population)

    def _cuda_batch_evaluate(self, population):
        """CUDA真正的批量并行评估"""
        try:
            return self._cuda_batch_evaluate_optimized(population)
        except Exception as e:
            print(f"CUDA批量评估失败，回退到逐个评估: {e}")
            # 回退到逐个评估但仍使用GPU
            fitnesses = []
            for individual in population:
                fitness = self.objective_function(individual)
                fitnesses.append(fitness)  # objective_function已经返回元组
            return fitnesses

    def _cuda_batch_evaluate_optimized(self, population):
        """优化的CUDA批量并行评估"""
        if not self.use_cuda or not population:
            return [self.objective_function(ind) for ind in population]  # 已经是元组格式

        # 转换种群为GPU数组
        pop_array = cp.array(population)  # shape: (pop_size, n_params)
        batch_size = len(population)

        # 分离初始SOC和OCV参数
        initial_socs = pop_array[:, 0]  # shape: (batch_size,)
        ocv_params = pop_array[:, 1:]   # shape: (batch_size, n_ocv_points)

        # 批量计算所有个体的适应度
        fitnesses = self._cuda_batch_simulate(initial_socs, ocv_params)

        # DEAP需要元组格式的适应度值
        return [(float(f),) for f in cp.asnumpy(fitnesses)]

    def _cuda_batch_simulate(self, initial_socs_batch, ocv_params_batch):
        """CUDA批量电池模拟 - 优化版本"""
        batch_size = len(initial_socs_batch)

        # 获取预加载的GPU数据（已经是float32类型）
        current_gpu = self.data_gpu['current']  # shape: (n_timesteps,)
        voltage_gpu = self.data_gpu['voltage']  # shape: (n_timesteps,)
        delta_t_gpu = self.data_gpu['time_diff']  # shape: (n_timesteps,)
        soc_points_gpu = self.data_gpu['soc_points']  # shape: (n_soc_points,)
        capacity_Ah = self.data_gpu['capacity_Ah']
        fixed_r0 = self.data_gpu['fixed_r0']

        # 使用广播避免大量内存复制
        # 计算SOC变化 - 使用广播而不是tile
        delta_soc = (current_gpu[cp.newaxis, :] * delta_t_gpu[cp.newaxis, :] / capacity_Ah * 100)

        # 为每个个体计算SOC轨迹
        initial_socs_expanded = initial_socs_batch[:, cp.newaxis]  # shape: (batch_size, 1)
        soc_batch = initial_socs_expanded - cp.cumsum(delta_soc, axis=1)
        soc_batch = cp.clip(soc_batch, 0, 100)  # shape: (batch_size, n_timesteps)

        # 批量线性插值计算OCV
        ocv_batch = self._cuda_batch_linear_interp(soc_batch, soc_points_gpu, ocv_params_batch)

        # 批量计算端电压 - 使用广播
        voltage_sim_batch = ocv_batch - current_gpu[cp.newaxis, :] * fixed_r0

        # 批量计算MAE - 使用广播
        mae_batch = cp.mean(cp.abs(voltage_sim_batch - voltage_gpu[cp.newaxis, :]), axis=1)

        # 批量约束检查
        constraint_penalties = self._cuda_batch_constraint_check(ocv_params_batch)

        # 总适应度 = MAE + 约束惩罚
        fitness_batch = mae_batch + constraint_penalties

        return fitness_batch

    def _cuda_batch_linear_interp(self, x_batch, xp, fp_batch):
        """CUDA批量线性插值

        Args:
            x_batch: shape (batch_size, n_points) - 要插值的x值
            xp: shape (n_soc_points,) - 已知x点
            fp_batch: shape (batch_size, n_soc_points) - 每个个体对应的y值

        Returns:
            shape (batch_size, n_points) - 插值结果
        """
        batch_size, n_points = x_batch.shape
        n_soc_points = len(xp)

        # 为每个时间点找到插值区间
        # 使用广播进行批量搜索
        x_expanded = x_batch[:, :, cp.newaxis]  # shape: (batch_size, n_points, 1)
        xp_expanded = xp[cp.newaxis, cp.newaxis, :]  # shape: (1, 1, n_soc_points)

        # 找到右侧索引
        indices = cp.sum(x_expanded >= xp_expanded, axis=2) - 1  # shape: (batch_size, n_points)
        indices = cp.clip(indices, 0, n_soc_points - 2)

        # 获取插值点
        batch_indices = cp.arange(batch_size)[:, cp.newaxis]  # shape: (batch_size, 1)

        # 左右端点的x值
        x0 = xp[indices]  # shape: (batch_size, n_points)
        x1 = xp[indices + 1]  # shape: (batch_size, n_points)

        # 左右端点的y值
        y0 = fp_batch[batch_indices, indices]  # shape: (batch_size, n_points)
        y1 = fp_batch[batch_indices, indices + 1]  # shape: (batch_size, n_points)

        # 线性插值
        t = (x_batch - x0) / (x1 - x0)
        result = y0 + t * (y1 - y0)

        return result

    def _cuda_batch_constraint_check(self, ocv_params_batch):
        """CUDA批量约束检查

        Args:
            ocv_params_batch: shape (batch_size, n_ocv_points)

        Returns:
            shape (batch_size,) - 每个个体的约束惩罚
        """
        batch_size = ocv_params_batch.shape[0]

        # 获取边界
        lower_bounds = cp.array(self.ocv_bounds[0])  # shape: (n_ocv_points,)
        upper_bounds = cp.array(self.ocv_bounds[1])  # shape: (n_ocv_points,)

        # 边界违反检查
        lower_violations = cp.sum(cp.maximum(lower_bounds - ocv_params_batch, 0), axis=1)
        upper_violations = cp.sum(cp.maximum(ocv_params_batch - upper_bounds, 0), axis=1)
        boundary_penalty = Config.OCV_BOUNDARY_PENALTY * (lower_violations + upper_violations)

        # 单调性检查
        ocv_diff = cp.diff(ocv_params_batch, axis=1)  # shape: (batch_size, n_ocv_points-1)
        monotonic_violations = cp.sum(cp.abs(cp.minimum(ocv_diff, 0)), axis=1)
        monotonic_penalty = Config.OCV_MONOTONIC_PENALTY * monotonic_violations

        return boundary_penalty + monotonic_penalty

    def _cpu_parallel_evaluate(self, population):
        """CPU多进程并行评估"""
        try:
            # 使用线程池而不是进程池，避免序列化问题
            with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                fitnesses = list(executor.map(self.objective_function, population))
            return fitnesses
        except Exception as e:
            print(f"并行评估失败，回退到串行: {e}")
            # 回退到串行评估
            return [self.objective_function(ind) for ind in population]

    def _plot_optimization_process(self, plot_filename=None):
        """绘制优化过程曲线"""
        if not self.optimization_history['generation']:
            return

        plt.figure(figsize=Config.OPTIMIZATION_PLOT_SIZE)

        # 1. 适应度变化曲线
        plt.subplot(1, 2, 1)
        plt.plot(self.optimization_history['generation'],
                self.optimization_history['best_fitness'], 'b-')
        plt.xlabel('代数')
        plt.ylabel('目标函数值')
        plt.title('GA优化过程收敛曲线')
        plt.grid(True)

        # 2. OCV曲线进化过程
        plt.subplot(1, 2, 2)
        for i in range(0, len(self.optimization_history['generation']), 10):
            plt.plot(self.soc_points, self.optimization_history['best_ocv'][i],
                    alpha=0.1, color='blue')
        plt.plot(self.soc_points, self.optimization_history['best_ocv'][-1],
                'r-', linewidth=2, label='最终OCV曲线')
        plt.plot(self.soc_points, self.default_ocv, 'g--', label='默认OCV曲线')
        plt.fill_between(self.soc_points,
                        self.ocv_bounds[0],
                        self.ocv_bounds[1],
                        color='gray', alpha=0.2, label='允许波动范围')
        plt.xlabel('SOC(%)')
        plt.ylabel('OCV(V)')
        plt.title('OCV曲线优化过程')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if plot_filename:
            plt.savefig(plot_filename, dpi=Config.PLOT_DPI, bbox_inches='tight')
            plt.close() # 关闭图形，避免内存泄漏
        else:
            plt.show()

    def optimize_parameters(self, use_actual_soc=None, soc_tolerance=None,
                           population_size=None, n_generations=None,
                           crossover_prob=None, mutation_prob=None,
                           plot_output_dir=None, file_date=None):
        """
        运行遗传算法参数优化

        参数:
            use_actual_soc: 是否使用实际SOC数据确定初始SOC范围 (默认从配置文件)
            soc_tolerance: SOC容差范围(%)，用于设定搜索边界 (默认从配置文件)
            population_size: 种群大小 (默认从配置文件)
            n_generations: 进化代数 (默认从配置文件)
            crossover_prob: 交叉概率 (默认从配置文件)
            mutation_prob: 变异概率 (默认从配置文件)
            plot_output_dir: 图表输出目录
            file_date: 当前处理文件对应的日期，用于命名图表文件
        """
        # 使用配置文件中的默认值
        use_actual_soc = use_actual_soc if use_actual_soc is not None else Config.USE_ACTUAL_SOC
        soc_tolerance = soc_tolerance if soc_tolerance is not None else Config.SOC_TOLERANCE
        population_size = population_size if population_size is not None else Config.POPULATION_SIZE
        n_generations = n_generations if n_generations is not None else Config.N_GENERATIONS
        crossover_prob = crossover_prob if crossover_prob is not None else Config.CROSSOVER_PROB
        mutation_prob = mutation_prob if mutation_prob is not None else Config.MUTATION_PROB
        if use_actual_soc:
            # 方案：使用实际SOC数据确定初始SOC范围
            # 自动检测SOC列
            soc_cols = [col for col in self.data.columns if 'SOC' in col]

            if soc_cols:
                soc_col_name = soc_cols[0]  # 使用第一个找到的SOC列
                print(f"检测到SOC列: {soc_col_name}")

                # 获取实际初始SOC
                actual_initial_soc = self.data[soc_col_name].iloc[0]
                print(f"数据集实际初始SOC: {actual_initial_soc:.2f}%")

                # 设定搜索范围：实际值 ± 容差
                soc_lower = max(0, actual_initial_soc - soc_tolerance)
                soc_upper = min(100, actual_initial_soc + soc_tolerance)

                self.lb = [soc_lower] + list(self.ocv_bounds[0])
                self.ub = [soc_upper] + list(self.ocv_bounds[1])

                print(f"初始SOC搜索范围: {soc_lower:.1f}% - {soc_upper:.1f}% (±{soc_tolerance}%容差)")
            else:
                print("警告: 未找到SOC列，使用默认范围")
                soc_lower, soc_upper = Config.DEFAULT_SOC_RANGE
                self.lb = [soc_lower] + list(self.ocv_bounds[0])
                self.ub = [soc_upper] + list(self.ocv_bounds[1])
                print(f"使用默认初始SOC搜索范围: {soc_lower}% - {soc_upper}%")
        else:
            # 原始方案：使用固定范围
            soc_lower, soc_upper = Config.DEFAULT_SOC_RANGE
            self.lb = [soc_lower] + list(self.ocv_bounds[0])
            self.ub = [soc_upper] + list(self.ocv_bounds[1])
            print(f"使用默认初始SOC搜索范围: {soc_lower}% - {soc_upper}%")

        # 设置遗传算法
        self._setup_ga(population_size)

        # 运行GA优化
        print(f"开始遗传算法优化(固定R0={self.fixed_r0:.4f}Ω)...")
        print(f"GA参数: 种群={population_size}, 代数={n_generations}, 交叉率={crossover_prob}, 变异率={mutation_prob}")

        # 记录开始时间
        start_time = time.time()

        # 创建初始种群
        population = self.toolbox.population(n=population_size)

        # 并行评估初始种群
        print("评估初始种群...")
        fitnesses = self._parallel_evaluate(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit

        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)

        # 进化过程
        generation_start_time = time.time()

        for generation in range(n_generations):
            gen_start = time.time()

            # 选择下一代
            offspring = self.toolbox.select(population, len(population))
            offspring = list(map(self.toolbox.clone, offspring))

            # 交叉和变异
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < crossover_prob:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values

            for mutant in offspring:
                if random.random() < mutation_prob:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values

            # 并行评估需要重新评估的个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            if invalid_ind:
                fitnesses = self._parallel_evaluate(invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit

            # 替换种群
            population[:] = offspring

            # 记录最佳个体
            best_ind = tools.selBest(population, 1)[0]
            best_fitness = best_ind.fitness.values[0]

            self.optimization_history['generation'].append(generation + 1)
            self.optimization_history['best_fitness'].append(best_fitness)
            self.optimization_history['best_ocv'].append(best_ind[1:].copy())

            # 打印进度（包含时间信息）
            gen_time = time.time() - gen_start
            elapsed_time = time.time() - generation_start_time

            if (generation + 1) % Config.PROGRESS_DISPLAY_INTERVAL == 0:
                record = stats.compile(population)
                avg_gen_time = elapsed_time / (generation + 1)
                eta = avg_gen_time * (n_generations - generation - 1)

                print(f"代数 {generation + 1}/{n_generations}: "
                      f"最佳={best_fitness:.6f}, 平均={record['avg']:.6f}, "
                      f"本代用时={gen_time:.2f}s, 已用时={elapsed_time:.1f}s, "
                      f"预计剩余={eta:.1f}s")
            elif (generation + 1) % Config.DETAILED_INFO_INTERVAL == 0:
                # 每N代显示更详细信息
                record = stats.compile(population)
                progress = (generation + 1) / n_generations * 100
                print(f"进度 {progress:.1f}%: 最佳适应度={best_fitness:.6f}, "
                      f"初始SOC={best_ind[0]:.2f}%, 已用时={elapsed_time:.1f}s")

        # 获取最优解
        best_individual = tools.selBest(population, 1)[0]
        best_params = list(best_individual)
        best_mae = best_individual.fitness.values[0]

        # 计算总运行时间
        total_time = time.time() - start_time

        print("\n" + "="*60)
        print("遗传算法优化完成!")
        print(f"最小MAE: {best_mae:.4f} V")
        print(f"总运行时间: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)")
        print(f"平均每代时间: {total_time/n_generations:.2f} 秒")
        print(f"计算设备: {'GPU (CUDA)' if self.use_cuda else 'CPU'}")
        print(f"并行线程数: {self.n_workers}")
        print("="*60)

        # 解包最优参数
        initial_soc = best_params[0]
        ocv_points = best_params[1:]

        print(f"\n初始SOC: {initial_soc:.2f}%")
        print("\n优化后的OCV曲线点:")
        for i in range(0, len(ocv_points), 5):  # 每5个点打印一个
            soc = self.soc_points[i]
            point = ocv_points[i]
            default = self.default_ocv[i]
            print(f"SOC {soc:.0f}%: {point:.4f}V (默认:{default:.2f}V, 偏差:{point-default:+.3f}V)")

        # 绘制优化过程曲线并保存
        print("\n生成优化过程图...")
        plot_start = time.time()
        if plot_output_dir and file_date:
            optimization_plot_filename = os.path.join(plot_output_dir, f"{file_date}_optimization_process.png")
            self._plot_optimization_process(plot_filename=optimization_plot_filename)
            print(f"   优化过程图已保存到: {optimization_plot_filename}")
        else:
            self._plot_optimization_process()
        plot_time = time.time() - plot_start

        # 用最优参数运行模拟并绘图并保存
        print(f"生成验证图... (优化过程图用时: {plot_time:.1f}s)")
        verify_start = time.time()
        if plot_output_dir and file_date:
            comparison_plot_filename = os.path.join(plot_output_dir, f"{file_date}_comparison.png")
            self.simulate_battery(best_params, plot_comparison=True, plot_filename=comparison_plot_filename)
            print(f"   验证图已保存到: {comparison_plot_filename}")
        else:
            self.simulate_battery(best_params, plot_comparison=True)
        verify_time = time.time() - verify_start

        print(f"\n时间统计:")
        print(f"   优化算法: {total_time:.2f}s")
        print(f"   优化过程图: {plot_time:.1f}s")
        print(f"   验证图: {verify_time:.1f}s")
        print(f"   总计: {total_time + plot_time + verify_time:.2f}s")

        return best_params, best_mae, total_time


def process_single_file_worker(file_path, output_plots_dir, current_col_pattern, voltage_col_pattern,
                              use_cuda, optimization_params):
    """
    独立的文件处理工作函数，用于多进程并行处理

    Args:
        file_path: 文件路径
        output_plots_dir: 图表输出目录
        current_col_pattern: 电流列名模式
        voltage_col_pattern: 电压列名模式
        use_cuda: 是否使用CUDA
        optimization_params: 优化参数字典

    Returns:
        dict: 处理结果
    """
    import os
    import pandas as pd
    from datetime import datetime
    import re

    def _extract_date_from_filename(filename):
        """从文件名提取日期信息"""
        # 格式1: YYYY-MM-DD
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
        if date_match:
            return date_match.group(1)

        # 格式2: YYYYMMDD
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            date_str = date_match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

        # 格式3: 使用文件名本身
        return os.path.splitext(filename)[0]

    def _detect_column_names(data):
        """智能检测电流和电压列名"""
        columns = data.columns.tolist()

        # 检测电流列
        current_candidates = [col for col in columns if '电流' in col or 'current' in col.lower()]
        if not current_candidates and current_col_pattern:
            current_candidates = [col for col in columns if current_col_pattern in col]

        # 检测电压列
        voltage_candidates = [col for col in columns if '电压' in col or 'voltage' in col.lower()]
        if not voltage_candidates and voltage_col_pattern:
            voltage_candidates = [col for col in columns if voltage_col_pattern in col]

        current_col = current_candidates[0] if current_candidates else None
        voltage_col = voltage_candidates[0] if voltage_candidates else None

        return current_col, voltage_col

    def _cleanup_gpu_memory():
        """清理GPU内存"""
        if CUDA_AVAILABLE:
            try:
                import cupy as cp
                cp.get_default_memory_pool().free_all_blocks()
                cp.get_default_pinned_memory_pool().free_all_blocks()
            except:
                pass

    def _cleanup_deap_creators():
        """清理DEAP的creator，避免重复创建错误"""
        try:
            from deap import creator
            if hasattr(creator, "FitnessMin"):
                del creator.FitnessMin
            if hasattr(creator, "Individual"):
                del creator.Individual
        except:
            pass

    file_name = os.path.basename(file_path)
    file_date = _extract_date_from_filename(file_name)

    try:
        # 加载数据
        data = pd.read_hdf(file_path)

        # 智能检测列名
        current_col, voltage_col = _detect_column_names(data)

        if not current_col or not voltage_col:
            raise ValueError(f"无法检测到电流列或电压列。可用列: {list(data.columns)}")

        # 创建优化器
        optimizer_ga = BatteryParameterOptimizerGA(
            data=data,
            current_col=current_col,
            voltage_col=voltage_col,
            use_cuda=use_cuda,
            n_workers=optimization_params.get('n_workers', 4)  # 减少每个进程的线程数
        )

        # 运行优化
        best_params, best_mae, total_time = optimizer_ga.optimize_parameters(
            use_actual_soc=optimization_params.get('use_actual_soc', True),
            soc_tolerance=optimization_params.get('soc_tolerance', 0),
            population_size=optimization_params.get('population_size', 300),
            n_generations=optimization_params.get('n_generations', 500),
            crossover_prob=optimization_params.get('crossover_prob', 0.7),
            mutation_prob=optimization_params.get('mutation_prob', 0.15),
            plot_output_dir=output_plots_dir,
            file_date=file_date
        )

        # 记录结果
        result = {
            'file_name': file_name,
            'date': file_date,
            'best_mae': float(best_mae),
            'initial_soc': float(best_params[0]),
            'ocv_points': [float(x) for x in best_params[1:]],
            'total_time_seconds': float(total_time),
            'current_col_used': current_col,
            'voltage_col_used': voltage_col,
            'data_shape': f"{data.shape[0]}x{data.shape[1]}",
            'time_range_start': str(data.index[0]),
            'time_range_end': str(data.index[-1]),
            'processing_timestamp': datetime.now().isoformat(),
            'gpu_used': optimizer_ga.use_cuda,
            'status': 'success'
        }

        return result

    except Exception as e:
        # 记录错误结果
        result = {
            'file_name': file_name,
            'date': file_date,
            'best_mae': 'Error',
            'initial_soc': 'Error',
            'ocv_points': 'Error',
            'total_time_seconds': 'Error',
            'current_col_used': 'Error',
            'voltage_col_used': 'Error',
            'data_shape': 'Error',
            'time_range_start': 'Error',
            'time_range_end': 'Error',
            'processing_timestamp': datetime.now().isoformat(),
            'gpu_used': 'Error',
            'status': 'failed',
            'error_message': str(e)
        }

        return result

    finally:
        # 清理资源
        _cleanup_gpu_memory()
        _cleanup_deap_creators()


class MultiDayBatteryOptimizer:
    """多日数据并行处理管理器"""

    def __init__(self, data_dir, output_plots_dir, results_csv_path,
                 current_col_pattern=None, voltage_col_pattern=None,
                 max_parallel_files=None):
        self.data_dir = data_dir
        self.output_plots_dir = output_plots_dir
        self.results_csv_path = results_csv_path
        self.current_col_pattern = current_col_pattern
        self.voltage_col_pattern = voltage_col_pattern

        # 并行处理配置
        self.max_parallel_files = max_parallel_files or Config.get_max_parallel_files()

        # 确保输出目录存在
        os.makedirs(output_plots_dir, exist_ok=True)

        # 处理结果存储
        self.all_results = []
        self.processing_stats = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_time': 0,
            'start_time': None,
            'parallel_files': self.max_parallel_files
        }

        # 进度监控
        self.progress_lock = threading.Lock()
        self.completed_files = 0

    def _detect_column_names(self, data):
        """智能检测电流和电压列名"""
        columns = data.columns.tolist()

        # 检测电流列
        current_candidates = [col for col in columns if '电流' in col or 'current' in col.lower()]
        if not current_candidates and self.current_col_pattern:
            current_candidates = [col for col in columns if self.current_col_pattern in col]

        # 检测电压列
        voltage_candidates = [col for col in columns if '电压' in col or 'voltage' in col.lower()]
        if not voltage_candidates and self.voltage_col_pattern:
            voltage_candidates = [col for col in columns if self.voltage_col_pattern in col]

        current_col = current_candidates[0] if current_candidates else None
        voltage_col = voltage_candidates[0] if voltage_candidates else None

        return current_col, voltage_col

    def _extract_date_from_filename(self, filename):
        """从文件名提取日期信息"""
        # 尝试多种日期格式
        import re

        # 格式1: YYYY-MM-DD
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
        if date_match:
            return date_match.group(1)

        # 格式2: YYYYMMDD
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            date_str = date_match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

        # 格式3: 使用文件名本身
        return os.path.splitext(filename)[0]

    def _cleanup_gpu_memory(self):
        """清理GPU内存"""
        if CUDA_AVAILABLE:
            try:
                cp.get_default_memory_pool().free_all_blocks()
                cp.get_default_pinned_memory_pool().free_all_blocks()
            except:
                pass

    def _cleanup_deap_creators(self):
        """清理DEAP的creator，避免重复创建错误"""
        try:
            if hasattr(creator, "FitnessMin"):
                del creator.FitnessMin
            if hasattr(creator, "Individual"):
                del creator.Individual
        except:
            pass



    def process_all_files(self, file_pattern="*.h5",
                         station_stack_cluster="127_1_1",
                         optimization_params=None, use_parallel=True):
        """处理指定站点_堆_簇的文件

        参数:
            file_pattern: 文件匹配模式
            station_stack_cluster: 站点_堆_簇格式，如"127_1_1"表示127站1号堆1号簇
            optimization_params: 优化参数字典
            use_parallel: 是否使用并行处理
        """
        # 默认优化参数（从配置文件获取）
        if optimization_params is None:
            optimization_params = {
                'use_actual_soc': Config.USE_ACTUAL_SOC,
                'soc_tolerance': Config.SOC_TOLERANCE,
                'population_size': Config.POPULATION_SIZE,
                'n_generations': Config.N_GENERATIONS,
                'crossover_prob': Config.CROSSOVER_PROB,
                'mutation_prob': Config.MUTATION_PROB,
                'n_workers': Config.N_WORKERS_PER_PROCESS
            }

        # 查找所有匹配的文件
        file_pattern_path = os.path.join(self.data_dir, file_pattern)
        all_h5_files = sorted(glob.glob(file_pattern_path))

        # 过滤出指定站点_堆_簇的文件
        h5_files = [f for f in all_h5_files if f"{station_stack_cluster}_" in os.path.basename(f)]

        if not h5_files:
            print(f"在目录 {self.data_dir} 中未找到匹配的文件")
            print(f"搜索条件: {station_stack_cluster}")
            return

        self.processing_stats['total_files'] = len(h5_files)
        self.processing_stats['start_time'] = time.time()

        print(f"检测到 {len(h5_files)} 个文件需要处理")
        print(f"处理范围: {station_stack_cluster} (127站1号堆1号簇电芯1)")
        print(f"数据目录: {self.data_dir}")
        print(f"输出目录: {self.output_plots_dir}")
        print(f"结果文件: {self.results_csv_path}")

        if use_parallel:
            print(f"并行处理模式: 最多同时处理 {self.max_parallel_files} 个文件")
            self._process_files_parallel(h5_files, optimization_params)
        else:
            print("串行处理模式")
            self._process_files_serial(h5_files, optimization_params)

        # 最终处理
        self._finalize_processing()

    def _process_files_parallel(self, h5_files, optimization_params):
        """并行处理文件"""
        # 创建部分应用的工作函数
        worker_func = partial(
            process_single_file_worker,
            output_plots_dir=self.output_plots_dir,
            current_col_pattern=self.current_col_pattern,
            voltage_col_pattern=self.voltage_col_pattern,
            use_cuda=CUDA_AVAILABLE,  # 每个进程独立决定是否使用CUDA
            optimization_params=optimization_params
        )

        # 使用进程池并行处理
        with ProcessPoolExecutor(max_workers=self.max_parallel_files) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(worker_func, file_path): file_path
                for file_path in h5_files
            }

            # 收集结果并显示进度
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                file_name = os.path.basename(file_path)

                try:
                    result = future.result()
                    self.all_results.append(result)

                    # 更新统计信息
                    with self.progress_lock:
                        self.completed_files += 1
                        if result['status'] == 'success':
                            self.processing_stats['successful_files'] += 1
                        else:
                            self.processing_stats['failed_files'] += 1

                        # 显示进度
                        progress = self.completed_files / len(h5_files) * 100
                        elapsed_time = time.time() - self.processing_stats['start_time']
                        avg_time_per_file = elapsed_time / self.completed_files
                        eta = avg_time_per_file * (len(h5_files) - self.completed_files)

                        print(f"进度 {progress:.1f}% ({self.completed_files}/{len(h5_files)}): "
                              f"{file_name} - {result['status']}")
                        if result['status'] == 'success':
                            print(f"   MAE: {result['best_mae']:.6f}, "
                                  f"处理时间: {result['total_time_seconds']:.1f}s, "
                                  f"预计剩余: {eta:.1f}s")
                        else:
                            print(f"   错误: {result.get('error_message', 'Unknown error')}")

                        # 定期保存结果
                        if self.completed_files % Config.RESULT_SAVE_INTERVAL == 0 or self.completed_files == len(h5_files):
                            self._save_results()

                except Exception as e:
                    print(f"处理文件 {file_name} 时发生异常: {e}")
                    # 创建错误结果记录
                    error_result = {
                        'file_name': file_name,
                        'status': 'failed',
                        'error_message': str(e),
                        'processing_timestamp': datetime.now().isoformat()
                    }
                    self.all_results.append(error_result)
                    self.processing_stats['failed_files'] += 1

    def _process_files_serial(self, h5_files, optimization_params):
        """串行处理文件（保留原有逻辑作为备选）"""
        for i, file_path in enumerate(h5_files, 1):
            # 使用工作函数处理单个文件
            result = process_single_file_worker(
                file_path=file_path,
                output_plots_dir=self.output_plots_dir,
                current_col_pattern=self.current_col_pattern,
                voltage_col_pattern=self.voltage_col_pattern,
                use_cuda=CUDA_AVAILABLE,
                optimization_params=optimization_params
            )
            self.all_results.append(result)

            # 更新统计信息
            if result['status'] == 'success':
                self.processing_stats['successful_files'] += 1
            else:
                self.processing_stats['failed_files'] += 1

            # 显示进度
            progress = i / len(h5_files) * 100
            elapsed_time = time.time() - self.processing_stats['start_time']
            avg_time_per_file = elapsed_time / i
            eta = avg_time_per_file * (len(h5_files) - i)

            print(f"进度 {progress:.1f}% ({i}/{len(h5_files)}): "
                  f"{os.path.basename(file_path)} - {result['status']}")
            if result['status'] == 'success':
                print(f"   MAE: {result['best_mae']:.6f}, "
                      f"处理时间: {result['total_time_seconds']:.1f}s, "
                      f"预计剩余: {eta:.1f}s")

            # 实时保存结果（防止程序中断丢失数据）
            if i % 5 == 0 or i == len(h5_files):  # 每5个文件或最后一个文件保存一次
                self._save_results()

    def _save_results(self):
        """保存处理结果到CSV"""
        if self.all_results:
            results_df = pd.DataFrame(self.all_results)
            results_df.to_csv(self.results_csv_path, index=False)

    def _finalize_processing(self):
        """完成处理后的总结"""
        total_time = time.time() - self.processing_stats['start_time']
        self.processing_stats['total_time'] = total_time

        print(f"\n{'='*80}")
        print("多日数据并行处理完成!")
        print(f"{'='*80}")
        print(f"处理统计:")
        print(f"   总文件数: {self.processing_stats['total_files']}")
        print(f"   成功处理: {self.processing_stats['successful_files']}")
        print(f"   处理失败: {self.processing_stats['failed_files']}")
        print(f"   成功率: {self.processing_stats['successful_files']/self.processing_stats['total_files']*100:.1f}%")
        print(f"   并行文件数: {self.processing_stats['parallel_files']}")
        print(f"   总处理时间: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        print(f"   平均每文件: {total_time/self.processing_stats['total_files']:.2f}秒")

        # 计算并行效率
        if self.processing_stats['successful_files'] > 0:
            theoretical_serial_time = total_time * self.max_parallel_files
            efficiency = min(100, theoretical_serial_time / total_time * 100 / self.max_parallel_files)
            print(f"   并行效率: {efficiency:.1f}%")

        if self.processing_stats['successful_files'] > 0:
            print(f"\n结果已保存到: {self.results_csv_path}")
            print(f"图表已保存到: {self.output_plots_dir}")

            # 生成汇总报告
            self._generate_summary_report()

    def _generate_summary_report(self):
        """生成汇总分析报告"""
        if not self.all_results:
            return

        successful_results = [r for r in self.all_results if r['status'] == 'success']

        if not successful_results:
            print("没有成功的处理结果，无法生成汇总报告")
            return

        print(f"\n生成汇总分析报告...")

        # 统计分析
        mae_values = [r['best_mae'] for r in successful_results]
        time_values = [r['total_time_seconds'] for r in successful_results]

        summary_stats = {
            'mae_mean': np.mean(mae_values),
            'mae_std': np.std(mae_values),
            'mae_min': np.min(mae_values),
            'mae_max': np.max(mae_values),
            'time_mean': np.mean(time_values),
            'time_total': np.sum(time_values),
            'processing_date': datetime.now().isoformat()
        }

        # 保存汇总统计
        summary_path = os.path.join(os.path.dirname(self.results_csv_path), 'processing_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary_stats, f, indent=2, ensure_ascii=False)

        print(f"   MAE统计: 均值={summary_stats['mae_mean']:.6f}, 标准差={summary_stats['mae_std']:.6f}")
        print(f"   MAE范围: {summary_stats['mae_min']:.6f} - {summary_stats['mae_max']:.6f}")
        print(f"   时间统计: 总计={summary_stats['time_total']:.1f}s, 平均={summary_stats['time_mean']:.1f}s")
        print(f"   汇总报告已保存到: {summary_path}")


# 多日数据并行处理主函数
if __name__ == "__main__":
    # 配置参数
    data_dir = "../database/"  # 数据文件目录
    output_plots_dir = "./output_plots_enhanced_parallel/" # 图表保存目录
    results_csv_path = "multi_day_optimization_results_enhanced_parallel.csv" # 结果CSV文件

    # 并行处理配置（从配置文件获取）
    max_parallel_files = Config.get_max_parallel_files()

    # 优化参数配置（从配置文件获取，可根据需要调整）
    optimization_params = {
        'use_actual_soc': Config.USE_ACTUAL_SOC,
        'soc_tolerance': Config.SOC_TOLERANCE,
        'population_size': Config.POPULATION_SIZE,
        'n_generations': Config.N_GENERATIONS,
        'crossover_prob': Config.CROSSOVER_PROB,
        'mutation_prob': Config.MUTATION_PROB,
        'n_workers': Config.N_WORKERS_PER_PROCESS
    }

    # 创建多日并行处理器
    processor = MultiDayBatteryOptimizer(
        data_dir=data_dir,
        output_plots_dir=output_plots_dir,
        results_csv_path=results_csv_path,
        current_col_pattern=Config.CURRENT_COL_PATTERN,
        voltage_col_pattern=Config.VOLTAGE_COL_PATTERN,
        max_parallel_files=max_parallel_files
    )

    print(f"{'='*80}")
    print("多日数据并行处理系统启动")
    print(f"{'='*80}")
    print(f"配置信息:")
    print(f"   数据目录: {data_dir}")
    print(f"   输出目录: {output_plots_dir}")
    print(f"   最大并行文件数: {max_parallel_files}")
    print(f"   种群大小: {optimization_params['population_size']}")
    print(f"   进化代数: {optimization_params['n_generations']}")
    print(f"   CUDA支持: {'是' if CUDA_AVAILABLE else '否'}")
    print(f"{'='*80}")

    # 开始并行处理127站1号堆1号簇电芯1的文件
    processor.process_all_files(
        file_pattern=Config.DEFAULT_FILE_PATTERN,
        station_stack_cluster=Config.DEFAULT_STATION_STACK_CLUSTER,
        optimization_params=optimization_params,
        use_parallel=True                 # 启用并行处理
    )

    print(f"\n{'='*80}")
    print("127站1号堆1号簇电芯1数据并行处理完成!")
    print(f"{'='*80}")
    print(f"详细结果请查看: {results_csv_path}")
    print(f"图表文件请查看: {output_plots_dir}")
    print(f"汇总报告请查看: processing_summary.json")
    print(f"{'='*80}")
